import React from 'react';
import Link, { LinkProps } from 'next/link';
import { cn } from '../../../lib/utils';
import { VariantProps, cva } from 'class-variance-authority';

export const buttonVariants = cva(
  'relative z-20 flex items-center justify-center rounded-[4px] transition',
  {
    variants: {
      variant: {
        primary: 'text-white bg-black group-hover:shadow-button-primary',
        secondary:
          'border border-black bg-white group-hover:shadow-button-secondary',
        blank:
          'text-black bg-transparent group-hover:bg-white group-hover:shadow-button-blank',
        back: 'text-black bg-white hover:bg-gray-100 active:bg-gray-200',
      },
      size: {
        s: 'py-1 px-2 text-sm min-w-[75px]',
        m: 'py-2 px-2 text-base min-w-[80px]',
        l: 'py-3 px-4 text-lg min-w-[100px]',
        mini: 'p-2',
      },
    },
    defaultVariants: {
      variant: 'primary',
    },
  }
);

export type TButtonVariants = VariantProps<typeof buttonVariants>;

type TCommonProps = {
  asLink?: boolean;
  testId?: string;
  asSquare?: boolean;
  beforeIcon?: React.ReactNode;
  afterIcon?: React.ReactNode;
  children?: React.ReactNode;
  bodyClassName?: string;
  className?: string;
} & TButtonVariants;

export type TButtonAsButtonProps = TCommonProps &
  React.ButtonHTMLAttributes<HTMLButtonElement>;
export type TButtonAsLinkProps = TCommonProps & LinkProps;

export type TButtonProps = TButtonAsButtonProps | TButtonAsLinkProps;

export const Button = ({
  testId,
  size = 'm',
  asSquare,
  variant = 'primary',
  beforeIcon,
  afterIcon,
  children,
  asLink = false,
  bodyClassName,
  className,
  ...props
}: TButtonProps) => {
  const classes = cn(
    'relative inline-flex group disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none'
  );

  const buttonBody = (
    <div className="relative">
      <div
        className={cn(
          buttonVariants({ variant, size }),
          'space-x-2',
          variant !== 'back' &&
            'group-hover:-translate-x-1 group-hover:-translate-y-1',
          asSquare && 'aspect-square size-9 min-w-0',
          bodyClassName
        )}
      >
        {beforeIcon && <span>{beforeIcon}</span>}
        {children}
        {afterIcon && <span>{afterIcon}</span>}
      </div>
      <div
        className={cn(
          'z-10 absolute block w-full h-full left-0 top-0 rounded-[4px]',
          variant === 'primary' && 'bg-primary',
          variant === 'secondary' && 'bg-tertiary',
          variant === 'blank' &&
            'bg-transparent transition-none group-hover:transition-all group-hover:bg-black delay-75',
          variant === 'back' && 'hidden'
        )}
      />
    </div>
  );

  if (asLink) {
    return (
      <Link
        data-testid={testId}
        className={cn(classes, className)}
        {...(props as TButtonAsLinkProps)}
      >
        {buttonBody}
      </Link>
    );
  }

  return (
    <button
      data-testid={testId}
      className={cn(classes, className)}
      {...(props as TButtonAsButtonProps)}
    >
      {buttonBody}
    </button>
  );
};

export default Button;
