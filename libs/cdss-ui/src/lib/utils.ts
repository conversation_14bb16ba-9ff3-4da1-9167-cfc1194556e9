import { type ClassValue, clsx } from 'clsx';
import { extendTailwindMerge } from 'tailwind-merge';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
dayjs.extend(duration);

const twMerge = extendTailwindMerge({
  extend: {
    classGroups: {
      'font-size': [
        'text-t0',
        'text-t1',
        'text-t2',
        'text-t3',
        'text-t4',
        'text-t5',
        'text-t6',
        'text-body',
        'text-remark',
        'text-footnote',
        'text-mini',
      ],
      h: ['field'],
    },
  },
});

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const replaceMdPattern = (input: string): string => {
  return input.replace(/\.md\)/g, ')');
};

export const addIdsToMarkdownHeadings = (markdown: string): string => {
  const headingRegex = /^(#+) (.*)$/gm;

  return markdown.replace(headingRegex, (match, hashes, headingText) => {
    // Slugify the heading text. You might want a more sophisticated slugify function for complex cases.
    const slug = headingText
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '-')
      // eslint-disable-next-line no-useless-escape
      .replace(/[^\w\-]+/g, '');
    // Return the modified heading with an ID in markdown format.
    // Markdown doesn't officially support IDs in the heading directly, so we need to use inline HTML.
    return `\n${hashes} ${headingText} <a id="${slug}" class="anchor-heading"></a>\n`;
  });
};

export const replaceClassWithClassName = (input: string): string => {
  return input.replace(/class="/g, 'className="');
};

export const formatMD = (input: string): string => {
  let result = input;
  result = replaceMdPattern(result);
  result = addIdsToMarkdownHeadings(result);
  result = replaceClassWithClassName(result);
  return result;
};

export const camelCaseToWords = (str: string) => {
  if (typeof str !== 'string') {
    return str;
  }
  return str
    .replace(/([a-z0-9])([A-Z])/g, '$1 $2') // insert a space before a capital letter if it's preceded by a lowercase letter or a digit
    .replace(/^./, function (str) {
      return str.toUpperCase();
    }) // uppercase the first character
    .trim(); // remove any leading or trailing spaces
};

export const formatAudioTime = (seconds: number) => {
  if (seconds === Infinity) {
    return '--';
  }
  const floored = Math.floor(seconds);
  let from = 14;
  let length = 5;
  // Display hours only if necessary.
  if (floored >= 3600) {
    from = 11;
    length = 8;
  }

  return new Date(floored * 1000).toISOString().substr(from, length);
};

export const getOptionLabelFromValue = (
  options: any[],
  value: string | number
) => {
  return options.find((option) => option?.value === value)?.label;
};

export const secondsToTimeDisplay = (
  seconds?: number,
  labels?: [string, string, string]
) => {
  if (!seconds) return '';
  const defaultLabels = ['h', 'm', 's'];
  const hrLabel = labels?.[0] ?? defaultLabels[0];
  const minLabel = labels?.[1] ?? defaultLabels[1];
  const secLabel = labels?.[2] ?? defaultLabels[2];
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  let result = '';
  if (hours > 0) {
    result += `${hours}${hrLabel} `;
  }
  if (minutes > 0) {
    result += `${minutes}${minLabel} `;
  }
  result += `${remainingSeconds}${secLabel}`;

  return result;
};

export const secondsToFormat = (seconds: number, format = 'mm:ss') => {
  return dayjs.duration(seconds, 'seconds').format(format);
};

export const downloadFileFromUrl = (url: string, fileName: string) => {
  if (!url) return;
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();
  link.remove();
};
