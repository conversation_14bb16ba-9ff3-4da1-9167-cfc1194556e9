{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/design-system/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/design-system/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/design-system"], "options": {"jestConfig": "libs/design-system/jest.config.ts", "passWithNoTests": true}}}}