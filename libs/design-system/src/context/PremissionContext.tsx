import { useContext } from 'react';
import { createRegisteredContext } from 'react-singleton-context';

// 定义权限类型
export type Permission = string[];

// 定义 Context 的类型
interface PermissionContextType {
  permissions: Permission;
  setPermissions: (permissions: Permission) => void;
}

// 创建 Context
export const PermissionContext = createRegisteredContext<PermissionContextType>(
  'PermissionContext',
  {
    permissions: [],
    setPermissions: () => null,
  }
);

// 创建一个自定义 Hook 方便使用
export const usePermission = () => {
  const context = useContext(PermissionContext);
  if (!context) {
    throw new Error('usePermission must be used within a PermissionProvider');
  }
  return context;
};
