import _ from 'lodash';
import { useState, useContext, ReactNode, useEffect } from 'react';
// import { useSearchParams } from 'next/navigation';
// import _ from 'lodash';
import { createRegisteredContext } from 'react-singleton-context';
import { useGlobalAudioPlayer } from 'react-use-audio-player';
import { useScrollToElement } from '../lib/hooks/useScrollToElement';
import { Direction } from 'react-resizable-panels/dist/declarations/src/types';

export const DUMMY_QM_FORM = {
  id: 'cse-1',
  questions: [
    {
      id: 's_1',
      title: '介紹',
      subSections: [
        {
          id: 'ss_1_1',
          title: '開始錄音和介紹',
          questions: [
            {
              id: 'q_1_1_1',
              question: '確認是否已經開始錄音。',
              questionType: 'fatal',
              answers: [
                {
                  id: '1_1_1_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '1_1_1_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_1_1_2',
              question: '提供日期和員工姓名。',
              questionType: 'fatal',
              answers: [
                {
                  id: '1_1_2_y',
                  label: '提供',
                  point: 1,
                },
                {
                  id: '1_1_2_n',
                  label: '未提供',
                  point: 0,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 's_2',
      title: '身份驗證',
      subSections: [
        {
          id: 'ss_2_1',
          title: '確認客戶資訊',
          questions: [
            {
              id: 'q_2_1_1',
              question: '要求客戶提供全名。',
              questionType: 'fatal',
              answers: [
                {
                  id: '2_1_1_y',
                  label: '提供',
                  point: 1,
                },
                {
                  id: '2_1_1_n',
                  label: '未提供',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_2_1_2',
              question: '要求客戶提供身份證號碼後四位。',
              questionType: 'fatal',
              answers: [
                {
                  id: '2_1_2_y',
                  label: '提供',
                  point: 1,
                },
                {
                  id: '2_1_2_n',
                  label: '未提供',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_2_1_3',
              question: '要求客戶提供出生日期和住址。',
              questionType: 'fatal',
              answers: [
                {
                  id: '2_1_3_y',
                  label: '提供',
                  point: 1,
                },
                {
                  id: '2_1_3_n',
                  label: '未提供',
                  point: 0,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 's_3',
      title: '弱勢客戶評估',
      subSections: [
        {
          id: 'ss_3_1',
          title: '評估結果確認',
          questions: [
            {
              id: 'q_3_1_1',
              question: '確認客戶是否有其他投資經驗。',
              questionType: 'fatal',
              answers: [
                {
                  id: '3_1_1_y',
                  label: '有',
                  point: 1,
                },
                {
                  id: '3_1_1_n',
                  label: '沒有',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_3_1_2',
              question: '確認客戶是否非弱勢客戶。',
              questionType: 'fatal',
              answers: [
                {
                  id: '3_1_2_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '3_1_2_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 's_4',
      title: '客戶見證人',
      subSections: [
        {
          id: 'ss_4_1',
          title: '見證人資格確認',
          questions: [
            {
              id: 'q_4_1_1',
              question: '確認見證人的全名。',
              questionType: 'fatal',
              answers: [
                {
                  id: '4_1_1_y',
                  label: '已確認',
                  point: 1,
                },
                {
                  id: '4_1_1_n',
                  label: '未確認',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_4_1_2',
              question: '確認見證人是否65歲以下。',
              questionType: 'fatal',
              answers: [
                {
                  id: '4_1_2_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '4_1_2_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_4_1_3',
              question: '確認見證人是否具有中學程度或以上的教育水平。',
              questionType: 'fatal',
              answers: [
                {
                  id: '4_1_3_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '4_1_3_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_4_1_4',
              question: '確認見證人對產品有足夠理解。',
              questionType: 'fatal',
              answers: [
                {
                  id: '4_1_4_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '4_1_4_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 's_5',
      title: '被授權人下單',
      subSections: [
        {
          id: 'ss_5_1',
          title: '被授權人資格確認',
          questions: [
            {
              id: 'q_5_1_1',
              question: '確認被授權人的全名。',
              questionType: 'fatal',
              answers: [
                {
                  id: '5_1_1_y',
                  label: '已確認',
                  point: 1,
                },
                {
                  id: '5_1_1_n',
                  label: '未確認',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_5_1_2',
              question: '確認被授權人對產品和衍生工具有認識。',
              questionType: 'fatal',
              answers: [
                {
                  id: '5_1_2_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '5_1_2_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 's_6',
      title: 'PICOP 類別',
      subSections: [
        {
          id: 'ss_6_1',
          title: '投資經驗確認',
          questions: [
            {
              id: 'q_6_1_1',
              question: '確認客戶是否非首次購買此類產品。',
              questionType: 'fatal',
              answers: [
                {
                  id: '6_1_1_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '6_1_1_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_6_1_2',
              question: '確認客戶已有此類投資產品的經驗。',
              questionType: 'fatal',
              answers: [
                {
                  id: '6_1_2_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '6_1_2_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 's_7',
      title: '產品適合性評估',
      subSections: [
        {
          id: 'ss_7_1',
          title: '投資適合性確認',
          questions: [
            {
              id: 'q_7_1_1',
              question: '確認客戶是否有足夠時間考慮投資適合性。',
              questionType: 'fatal',
              answers: [
                {
                  id: '7_1_1_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '7_1_1_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_7_1_2',
              question: '確認客戶是否理解此產品的特質、運作模式及相關風險。',
              questionType: 'fatal',
              answers: [
                {
                  id: '7_1_2_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '7_1_2_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_7_1_3',
              question: '確認客戶是否接受潛在損失。',
              questionType: 'fatal',
              answers: [
                {
                  id: '7_1_3_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '7_1_3_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 's_8',
      title: '投資評估與最終確認',
      subSections: [
        {
          id: 'ss_8_1',
          title: '最終投資確認',
          questions: [
            {
              id: 'q_8_1_1',
              question: '確認客戶同意適合性評估結果。',
              questionType: 'fatal',
              answers: [
                {
                  id: '8_1_1_y',
                  label: '同意',
                  point: 1,
                },
                {
                  id: '8_1_1_n',
                  label: '不同意',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_8_1_2',
              question: '確認交易金額和戶口號碼。',
              questionType: 'fatal',
              answers: [
                {
                  id: '8_1_2_y',
                  label: '已確認',
                  point: 1,
                },
                {
                  id: '8_1_2_n',
                  label: '未確認',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_8_1_3',
              question: '確認客戶是否接受交易條件並願意進行。',
              questionType: 'fatal',
              answers: [
                {
                  id: '8_1_3_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '8_1_3_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_8_1_4',
              question: '確認客戶是否了解申請一旦接受便不可撤回。',
              questionType: 'fatal',
              answers: [
                {
                  id: '8_1_4_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '8_1_4_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
            {
              id: 'q_8_1_5',
              question: '確認是否已通知客戶產品的發行結果。',
              questionType: 'fatal',
              answers: [
                {
                  id: '8_1_5_y',
                  label: '是',
                  point: 1,
                },
                {
                  id: '8_1_5_n',
                  label: '否',
                  point: 0,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 's_04',
      title: 'Overall comments',
      section: 'Overall comments',
      subSections: [
        {
          id: 'ss_04_1',
          title: 'Overall comments',
          questions: [
            {
              id: 'q04_1_1',
              questionType: 'other',
              question: 'Leave your overall comments here:',
              type: 'textarea',
            },
          ],
        },
      ],
    },
    {
      id: 's_05',
      section: 'Action',
      title: 'Action',
      subSections: [
        {
          id: 'ss_05_1',
          title: 'Follow-up Action',
          questions: [
            {
              id: 'q05_1_1',
              questionType: 'other',
              question: 'Follow-up action:',
              type: 'select',
              answers: [
                {
                  id: '05_1_1_1',
                  label: 'No Action.',
                },
                {
                  id: '05_1_1_2',
                  label: 'Training Required.',
                },
                {
                  id: '05_1_1_3',
                  label: 'Re-evaluation Required.',
                },
                {
                  id: '05_1_1_4',
                  label: 'Termination Required.',
                },
                {
                  id: '05_1_1_5',
                  label: 'Other.',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 's_06',
      title: 'Override Result',
      subSections: [
        {
          id: 'ss_06_2',
          title: 'Override Result',
          questions: [
            {
              id: 'q5_02_1',
              questionType: 'other',
              question: 'Override result to:',
              type: 'radio',
              answers: [
                {
                  id: '06_2_1_1',
                  label: 'Passed',
                },
                {
                  id: '06_2_1_2',
                  label: 'Failed',
                },
              ],
            },
            {
              id: 'q06_2_2',
              questionType: 'other',
              question: 'Reason for overriding:',
              type: 'textarea',
            },
          ],
        },
      ],
    },
  ],
};

type TQMContextType = {
  activeQMSection?: string;
  qaFormAnswers?: any;
  openedQA?: string | null | undefined;
  formId?: string;
  showStrandScript?: boolean;
  highlightedQuestion?: string[] | null | undefined;
  tempTime?: any;
  scrollingStage?: boolean;
  resultFilter?: string;
  highlightedScript: string;
  layoutMode?: string;
  openedAdminSOPStep?: any;
  expandedQAResults?: string[];
  standardScriptDirection: Direction;
  currentPosition: number;
  showTempTime: () => void;
  closeTempTime: () => void;
  updateTempStartTime: (time: any) => void;
  updateTempEndTime: (time: any) => void;
  updateQMActiveSection: (sec: string) => void;
  updateQMAnswer: (
    qaId: string,
    stageId: string,
    sectionId: string,
    questionId: string,
    answer: string
  ) => void;
  openQA: (qaId: string, formId?: string) => void;
  closeQA: () => void;
  highlightQuestion: (questionIds: string[], secId?: string) => void;
  getScrollToStageRef: (stage: string) => any;
  scrollToStageClickHandler: (stage: string) => void;
  getScrollToScriptRef: (stage: string) => any;
  scrollToScriptClickHandler: (stage: string) => void;
  updateResultFilter: (filter: string) => void;
  highlightScript: (scriptId: string) => void;
  updateLayoutMode: (mode: string) => void;
  toggleExpandedQAResult: (qaId: string, open: boolean) => void;
  updateOpenedAdminSOPStep: (step: any) => void;
  toggleStrandScript?: () => void;
  toggleDirection: () => void;
  updateCurrentPosition: (position: number) => void;
};

const QMContext = createRegisteredContext<TQMContextType>('QMContext', {
  activeQMSection: undefined,
  qaFormAnswers: {},
  openedQA: null,
  formId: undefined,
  showStrandScript: false,
  highlightedQuestion: null,
  tempTime: {
    startTime: 0,
    endTime: 0,
    show: false,
  },
  scrollingStage: false,
  resultFilter: '',
  highlightedScript: '',
  layoutMode: '1',
  openedAdminSOPStep: null,
  expandedQAResults: [],
  standardScriptDirection: 'horizontal',
  currentPosition: 0,
  showTempTime: () => null,
  closeTempTime: () => null,
  updateTempStartTime: () => null,
  updateTempEndTime: () => null,
  updateQMActiveSection: () => null,
  updateQMAnswer: () => null,
  openQA: () => null,
  closeQA: () => null,
  highlightQuestion: () => null,
  getScrollToStageRef: () => null,
  scrollToStageClickHandler: () => null,
  getScrollToScriptRef: () => null,
  scrollToScriptClickHandler: () => null,
  updateResultFilter: () => null,
  highlightScript: () => null,
  updateLayoutMode: () => null,
  toggleExpandedQAResult: () => null,
  updateOpenedAdminSOPStep: () => null,
  toggleStrandScript: () => null,
  toggleDirection: () => null,
  updateCurrentPosition: () => null,
});

export const QMProvider = ({
  children,
  defaultFormAns,
}: {
  children: ReactNode;
  defaultFormAns: any;
}) => {
  const { duration } = useGlobalAudioPlayer();
  const [qaFormAnswers, setQAFormAnswers] = useState<any>(defaultFormAns);
  const [activeQMSection, setActiveQMSection] = useState<string>('');
  const [currentPosition, setCurrentPosition] = useState<number>(0);
  const [layoutMode, setLayoutMode] = useState<string>('2');
  const [expandedQAResults, setExpandedQAResults] = useState<string[]>([]);
  const [openedQA, setOpenedQA] = useState<string | null | undefined>();
  const [formId, setFormId] = useState<string>('');
  const [showStrandScript, setShowStrandScript] = useState<boolean>(false);
  const [standardScriptDirection, setStandardScriptDirection] = useState<
    'horizontal' | 'vertical'
  >('vertical');
  const [highlightedQuestion, setHighlightedQuestion] = useState<
    string[] | null | undefined
  >();
  const [highlightedScript, setHighlightedScript] = useState<string>('');
  const [resultFilter, setResultFilter] = useState<string>('');
  const [scrollingStage, setScrollingStage] = useState(false);

  const [openedAdminSOPStep, setOpenedAdminSOPStep] = useState<string | null>(
    null
  );

  useEffect(() => {
    if (highlightedQuestion) {
      setTimeout(() => {
        setHighlightedQuestion(null);
      }, 3000);
    }
  }, [highlightedQuestion]);

  useEffect(() => {
    if (localStorage.getItem('layoutMode')) {
      setLayoutMode(localStorage.getItem('layoutMode') || '2');
    }
  }, []);
  useEffect(() => {
    if (localStorage.getItem('layoutMode') !== layoutMode) {
      localStorage.setItem('layoutMode', layoutMode);
    }
  }, [layoutMode]);

  // useEffect(() => {
  //   if (highlightedScript) {
  //     setTimeout(() => {
  //       setHighlightedScript('');
  //     }, 3000);
  //   }
  // }, [highlightedScript]);

  const highlightQuestion = (questionIds: string[], secId?: string) => {
    setHighlightedQuestion(questionIds);
    if (secId) {
      setActiveQMSection(secId);
    }
  };

  const [tempTime, setTempTime] = useState<any>({
    startTime: 0,
    endTime: 50,
    show: false,
  });
  const updateTempStartTime = (time: any) => {
    setTempTime((prev: any) => ({
      ...prev,
      startTime: time,
    }));
  };
  const updateTempEndTime = (time: any) => {
    setTempTime((prev: any) => ({
      ...prev,
      endTime: time,
    }));
  };

  const closeTempTime = () => {
    setTempTime({
      startTime: 0,
      endTime: duration,
      show: false,
    });
  };
  const showTempTime = () => {
    setTempTime({
      startTime: 0,
      endTime: duration,
      show: true,
    });
  };

  const openQA = (qaId: string, formId?: string) => {
    setOpenedQA(qaId);
    if (formId && formId !== '') {
      setFormId(formId);
    }
  };
  const closeQA = () => {
    setOpenedQA(null);
    setFormId('');
    setShowStrandScript(false);
  };

  const updateQMActiveSection = (section: string) => {
    setActiveQMSection(section);
  };

  const updateQMAnswer = (
    qaId: string,
    stageId: string,
    sectionId: string,
    questionId: string,
    answer: string
  ) => {
    setQAFormAnswers((prev: any) => {
      const newAns = { ...prev };
      _.set(newAns, `${qaId}.${stageId}.${sectionId}.${questionId}`, answer);
      return newAns;
    });
  };

  const updateOpenedAdminSOPStep = (stepId: string) => {
    setOpenedAdminSOPStep(stepId);
  };

  const stageNames =
    DUMMY_QM_FORM?.questions?.map((sec) => `stage-${sec.id}`) || [];
  const scriptNames =
    DUMMY_QM_FORM?.questions?.map((sec) => `script-${sec.id}`) || [];
  const {
    getScrollToElementRef: getScrollToStageRef,
    scrollToElementClickHandler: scrollToStageClickHandler,
  } = useScrollToElement(
    stageNames, //array of strings
    {
      behavior: 'smooth',
    } //this is optional - behavior: smooth is used by default
  );
  const {
    getScrollToElementRef: getScrollToScriptRef,
    scrollToElementClickHandler: scrollToScriptClickHandler,
  } = useScrollToElement(
    scriptNames, //array of strings
    {
      behavior: 'auto',
    } //this is optional - behavior: smooth is used by default
  );

  // set show strand script
  const toggleStrandScript = () => {
    setShowStrandScript(!showStrandScript);
  };
  const toggleDirection = () => {
    setStandardScriptDirection((prevDirection) =>
      prevDirection === 'horizontal' ? 'vertical' : 'horizontal'
    );
  };

  const updateCurrentPosition = (position: number) => {
    setCurrentPosition(position);
  };

  return (
    <QMContext.Provider
      value={{
        activeQMSection,
        qaFormAnswers,
        highlightedQuestion,
        openedQA,
        tempTime,
        scrollingStage,
        formId,
        standardScriptDirection,
        showStrandScript,
        currentPosition,
        updateCurrentPosition,
        toggleDirection,
        toggleStrandScript,
        resultFilter,
        highlightedScript,
        layoutMode,
        openedAdminSOPStep,
        expandedQAResults,
        updateQMActiveSection,
        updateQMAnswer,
        openQA,
        closeQA,
        showTempTime,
        closeTempTime,
        updateTempStartTime,
        updateTempEndTime,
        highlightQuestion,
        getScrollToStageRef,
        scrollToStageClickHandler: (stage: string) => {
          setScrollingStage(true);
          scrollToStageClickHandler(stage);
          setTimeout(() => {
            setScrollingStage(false);
          }, 500);
        },
        getScrollToScriptRef,
        scrollToScriptClickHandler,
        updateResultFilter: (filter: string) => setResultFilter(filter),
        highlightScript: (scriptId: string) => setHighlightedScript(scriptId),
        updateLayoutMode: (mode: string) => setLayoutMode(mode),
        toggleExpandedQAResult: (qaId: string, open: boolean) => {
          if (open) {
            setExpandedQAResults((prev) => [...prev, qaId]);
          } else {
            setExpandedQAResults((prev) => prev.filter((id) => id !== qaId));
          }
        },
        updateOpenedAdminSOPStep,
      }}
    >
      {children}
    </QMContext.Provider>
  );
};

export const useQM = () => useContext(QMContext);
