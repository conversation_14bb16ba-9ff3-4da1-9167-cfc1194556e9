import { ReactNode, useEffect, useState } from 'react';
import { Permission, PermissionContext } from './PremissionContext';

interface PermissionProviderProps {
  children: ReactNode;
}

export const PermissionProvider: React.FC<PermissionProviderProps> = ({
  children,
}) => {
  const [permissions, setPermissions] = useState<Permission>(() => {
    // 初始化时从 localStorage 加载
    if (typeof window !== 'undefined') {
      const storedPermissions = localStorage.getItem('permissions');
      return storedPermissions ? JSON.parse(storedPermissions) : [];
    }
    return [];
  });

  // 监听权限变化并存储到 localStorage
  useEffect(() => {
    localStorage.setItem('permissions', JSON.stringify(permissions));
  }, [permissions]);

  return (
    <PermissionContext.Provider value={{ permissions, setPermissions }}>
      {children}
    </PermissionContext.Provider>
  );
};
