import dayjs from 'dayjs';
import { useContext, ReactNode, useState } from 'react';
import { createRegisteredContext } from 'react-singleton-context';

export const DUMMY_USERS = [
  {
    id: 'dummy-user-1',
    name: 'Agent 1',
    username: '<EMAIL>',
    usergroup: ['dummy-user-group-1'],
    role: [],
    isActive: true,
    type: 'Genesys Cloud',
    createdBy: 'Admin',
    createdAt: '2024-07-04T00:00:00.000Z',
    updatedBy: 'Admin',
    updatedAt: '2024-07-05T00:00:00.000Z',
  },
  {
    id: 'dummy-user-2',
    name: 'Supervisor 1',
    username: '<EMAIL>',
    usergroup: ['dummy-user-group-1', 'dummy-user-group-3'],
    role: [],
    isActive: true,
    type: 'Genesys Cloud',
    createdBy: 'Admin',
    createdAt: '2024-07-02T00:00:00.000Z',
    updatedBy: 'Admin',
    updatedAt: '2024-07-03T00:00:00.000Z',
  },
  {
    id: 'dummy-user-21',
    name: 'Agent 2',
    username: '<EMAIL>',
    usergroup: ['dummy-user-group-2'],
    role: [],
    isActive: true,
    type: 'Pure Engage',
    createdBy: 'Admin',
    createdAt: '2024-07-06T00:00:00.000Z',
    updatedBy: 'Admin',
    updatedAt: '2024-07-07T00:00:00.000Z',
  },
  {
    id: 'dummy-user-22',
    name: 'Supervisor 2',
    username: '<EMAIL>',
    usergroup: ['dummy-user-group-2', 'dummy-user-group-3'],
    role: [],
    isActive: true,
    type: 'Pure Engage',
    createdBy: 'Admin',
    createdAt: '2024-07-08T00:00:00.000Z',
    updatedBy: 'Admin',
    updatedAt: '2024-07-09T00:00:00.000Z',
  },
  {
    id: 'dummy-user-3',
    name: 'GC Admin',
    username: '<EMAIL>',
    usergroup: ['dummy-user-group-3'],
    role: [],
    isActive: true,
    type: 'CDSS',
    createdBy: 'Admin',
    createdAt: '2024-07-10T00:00:00.000Z',
    updatedBy: 'Admin',
    updatedAt: '2024-07-11T00:00:00.000Z',
  },
];

export const DUMMY_USER_GROUPS = [
  {
    id: 'dummy-user-group-1',
    name: 'Agent Group 1',
    roles: ['dummy-role-1'],
  },
  {
    id: 'dummy-user-group-2',
    name: 'Agent Group 2',
    roles: ['dummy-role-2'],
  },
  {
    id: 'dummy-user-group-3',
    name: 'Management Group',
    roles: ['dummy-role-3'],
  },
];

export const DUMMY_PERMISSIONS = [
  {
    id: 'ctint-mf-interaction.application.visit',
    name: 'Interaction Application Visit',
    desc: 'User with this permission can visit and see the buttons that go to Interaction Portal',
  },
  {
    id: 'ctint-mf-interaction.recording.download',
    name: 'Interaction Recording Download',
    desc: 'User with this permission can download recordings (zips with audio file) in the Interaction Portal.',
  },
  {
    id: 'ctint-mf-tts.application.visit',
    name: 'TTS Application Visit',
    desc: 'User with this permission can visit and see the buttons that go to Text to speech (TTS).',
  },
  {
    id: 'ctint-mf-msg.application.visit',
    name: 'Message Application Visit',
    desc: 'User with this permission can visit and see the buttons that go to Message Portal.',
  },
  {
    id: 'ctint-mf-admin.application.visit',
    name: 'CDSS Admin Portal Visit',
    desc: 'User with this permission can visit and see the buttons that go to CDSS Admin Portal.',
  },
  ...['View', 'Edit', 'Delete'].map((action) => {
    return {
      id: `ctint-mf-admin.user.${action.toLowerCase()}`,
      name: `CDSS Admin User ${action}`,
      desc: `User with this permission can ${action.toLowerCase()} users in CDSS Admin Portal.`,
    };
  }),
];

export const DUMMY_ROLES = [
  {
    id: 'dummy-role-1',
    name: 'Agent',
    permissions: [
      'ctint-mf-interaction.application.visit',
      'ctint-mf-interaction.recording.download',
    ],
  },
  {
    id: 'dummy-role-2',
    name: 'Supervisor',
    permissions: DUMMY_PERMISSIONS?.filter(
      (p) => p.id.indexOf('edit') < 0
    )?.map((p) => p.id),
  },
  {
    id: 'dummy-role-3',
    name: 'Admin',
    permissions: DUMMY_PERMISSIONS?.map((p) => p.id),
  },
];

export const DUMMY_AUDITS = {
  data: {
    logs: [
      {
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },{
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },{
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },{
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },{
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },{
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },{
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },{
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },{
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },{
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },{
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },{
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },{
        logId: '0bcbe71c-e3b1-422d-bbf6-444f0984d6ca',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T08:24:04.071Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
      {
        logId: 'e9efbd76-23ee-4365-85b3-306ee8647f37',
        userId: 'b44b490c-84f6-4fea-a867-4e226dcc218a',
        eventType: 'LOGIN_ATTEMPT',
        eventTimestamp: '2024-10-18T10:42:26.501Z',
        ipAddress: 'body.IpAddress',
        userAgent: '<EMAIL>',
        browser: 'body.Browser',
        operatingSystem: 'body.OperatingSystem',
        device: 'body.Device',
        failureReason: 'body.FailureReason',
        additionalInfo: 'body.AdditionalInfo',
      },
    ],
    total: 100,
  },
  error: '',
  isSuccess: true,
};

type TCDSSAdminContextType = {
  users: any[];
  userGroups: any[];
  roles: any[];
  permissions: any[];
  audits: any;
  openedEntity: any;
  updateOpenedEntity: (entity: any) => void;
};

const CDSSAdminContext = createRegisteredContext<TCDSSAdminContextType>(
  'CDSSAdminContext',
  {
    users: [],
    userGroups: [],
    roles: [],
    permissions: [],
    audits: {},
    openedEntity: null,
    updateOpenedEntity: () => null,
  }
);

export const CDSSAdminProvider = ({ children }: { children: ReactNode }) => {
  const [openedEntity, setOpenedEntity] = useState<any | null>(null);
  const updateOpenedEntity = (entity: any) => {
    setOpenedEntity(entity);
  };
  const adjustList = (list: any[]) => {
    return list.map((item) => {
      return {
        ...item,
        createdAt: item?.createdAt || dayjs().toISOString(),
        createdBy: item?.createdBy || 'System',
        updatedAt: item?.updatedAt || dayjs().toISOString(),
        updatedBy: item?.updatedBy || 'System',
      };
    });
  };

  return (
    <CDSSAdminContext.Provider
      value={{
        users: adjustList(DUMMY_USERS),
        userGroups: adjustList(
          DUMMY_USER_GROUPS?.map((group) => ({
            ...group,
            roles: group?.roles?.map(
              (roleId) => DUMMY_ROLES?.find((role) => role.id === roleId)?.name
            ),
          }))
        ),
        roles: adjustList(
          DUMMY_ROLES?.map((role) => ({
            ...role,
            permissions: role?.permissions?.map(
              (permId) =>
                DUMMY_PERMISSIONS?.find((perm) => perm.id === permId)?.name
            ),
          }))
        ),
        permissions: adjustList(DUMMY_PERMISSIONS),
        audits: DUMMY_AUDITS,
        openedEntity,
        updateOpenedEntity,
      }}
    >
      {children}
    </CDSSAdminContext.Provider>
  );
};

export const useCDSSAdmin = () => useContext(CDSSAdminContext);
