import { createContext, useContext } from 'react';

const EvaluationFormListContext = createContext<unknown>({}); // 使用 unknown 作为默认类型

export const useEvaluationFormList = <T,>() =>
  useContext(EvaluationFormListContext) as T;

export const EvaluationFormListProvider = <T,>({
  children,
  formList,
}: {
  children: React.ReactNode;
  formList: T;
}): JSX.Element => {
  return (
    <EvaluationFormListContext.Provider value={formList}>
      {children}
    </EvaluationFormListContext.Provider>
  );
};
