import { useRouteHandler } from '../../../context/RouteContext';

export type TBreadcrumbItem = {
  link?: string;
  onClick?: () => void;
  label: string;
};

type TBreadcrumbProps = {
  items: TBreadcrumbItem[];
};

const Breadcrumb: React.FC<TBreadcrumbProps> = ({ items }) => {
  const { toPath } = useRouteHandler();

  return (
    <div className="flex items-center justify-center">
      {items.map((item: TBreadcrumbItem, index) => {
        return (
          <div
            key={item.label}
            className="cursor-pointer mr-1"
            onClick={() =>
              item?.onClick ? item.onClick() : toPath(`${item.link}`)
            }
          >
            {item.label}
            {index !== items.length - 1 && <span> / </span>}
          </div>
        );
      })}
    </div>
  );
};

export default Breadcrumb;
