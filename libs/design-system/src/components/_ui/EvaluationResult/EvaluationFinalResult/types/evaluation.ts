import { ManualDetailItem } from '../../EvaluationResultItem/types/evaluation';

// 评估结果，用于display的结构
export interface EvaluationResult {
  stepId: string;
  stepName: string;
  subStep: EvaluationResultSubStep[];
}

export interface EvaluationResultSubStep {
  subStepId: string;
  subStepName: string;
  nlpResultId: string;
  autoRate: number;
  autoResult: string;
  manualResult: string;
  manualComment: string;
  manualUpdateTime?: string;
  notes?: string;
  recordingTimeLocation: string;
  manualDetailList?: ManualDetailItem[];
}

export interface ManualDetailList {
  manualDetailId: string;
  type: string;
  referenceId: string;
  tenant: string;
  platform: string;
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
  manualResult: string;
  comment: string;
}
