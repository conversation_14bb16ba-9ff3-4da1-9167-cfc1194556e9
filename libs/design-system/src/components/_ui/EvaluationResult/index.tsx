import { timeStringToSeconds } from '../../../lib/utils';
import EvaluationResultItem from './EvaluationResultItem';
import { EvaluationResult as TEvaluationResult } from './EvaluationResultItem/types/evaluation';
import { useTranslation } from 'react-i18next';

interface EvaluationResultProps {
  evaluationId: string;
  currentPosition: number;
  data: TEvaluationResult;
  onItemSubmit: (
    comment: string,
    manualResult: string,
    nlpResultId: string
  ) => void;
}

const EvaluationResult: React.FC<EvaluationResultProps> = ({
  evaluationId,
  currentPosition,
  data,
  onItemSubmit,
}) => {
  const { t } = useTranslation();

  const extractStepNumber = (stepName: string): string =>
    stepName.replace(/^Step\s+/i, '') || '';
  const constructTranslatedStepName = (stepNumber: string): string =>
    `${t('evaluation.step')} ${stepNumber}`;
  const translatedStepName = constructTranslatedStepName(
    extractStepNumber(data.stepName || '')
  );

  return (
    <>
      {data.subStep &&
        data.subStep.length > 0 && ( // 如果subStep为空，则不显示
          <section className="mx-4 my-2">
            <h2 className="text-[16px] font-bold mb-2">{translatedStepName}</h2>
            {data.subStep.map((subStep) => (
              <EvaluationResultItem
                key={`${subStep.subStepId}-${subStep.subStepName}`}
                subStep={subStep}
                evaluationId={evaluationId}
                isFocus={
                  !!(
                    subStep.timerStart &&
                    subStep.timerEnd &&
                    currentPosition >=
                      timeStringToSeconds(subStep.timerStart) &&
                    currentPosition < timeStringToSeconds(subStep.timerEnd)
                  )
                }
                onSubmit={(comment, manualResult) =>
                  onItemSubmit(comment, manualResult, subStep.nlpResultId)
                }
              />
            ))}
          </section>
        )}
    </>
  );
};

export default EvaluationResult;
