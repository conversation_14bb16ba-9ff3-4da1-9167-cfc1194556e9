import dayjs from 'dayjs';
import { EvaluationResultSubStep } from '../../EvaluationFinalResult/types/evaluation';
import IconArrowFullDown from '../../../Icon/IconArrowFullDown';
import { useState } from 'react';
import EvaluationResultCommentItem from './EvaluationResultCommentItem';

interface EvaluationResultCommentProps {
  subStep: EvaluationResultSubStep;
}

const EvaluationResultComment: React.FC<EvaluationResultCommentProps> = ({
  subStep,
}) => {
  const [opened, setOpened] = useState<boolean>(false); // 是否展开
  return (
    <>
      {subStep.manualDetailList &&
        subStep.manualDetailList.length > 0 &&
        subStep.manualDetailList.map((item, index) => {
          if (index === 0) {
            return (
              <EvaluationResultCommentItem
                key={item.manualDetailId}
                data={item}
                showMore={
                  subStep?.manualDetailList !== undefined &&
                  subStep?.manualDetailList?.length > 1
                }
                opened={opened}
                setOpened={setOpened}
              />
            );
          } else {
            return (
              opened && (
                <div
                  className={`transition-opacity duration-300 ${opened ? 'opacity-100' : 'opacity-0'}`}
                >
                  <EvaluationResultCommentItem
                    key={item.manualDetailId}
                    data={item}
                    showMore={false}
                  />
                </div>
              )
            );
          }
        })}
    </>
  );
};

export default EvaluationResultComment;
