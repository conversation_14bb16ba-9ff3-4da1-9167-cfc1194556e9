import { useState } from 'react';
import { EvaluationResultSubStep } from '../types/evaluation';
import * as ToggleGroup from '@radix-ui/react-toggle-group';
import { useGlobalAudioPlayer } from 'react-use-audio-player';
import { timeStringToSeconds } from '../../../../../lib/utils';
import { CommonPermission } from '../../../../../@types/CommonPermission';
import { useRole } from '../../../../../context/RoleContext';
import { usePermission } from '../../../../../context/PremissionContext';

interface EvaluationUpdatorV2Props {
  subStep: EvaluationResultSubStep;
  evaluationId: string;
  onSubmit: (comment: string, manualResult: string) => void;
}

const EvaluationUpdatorV2: React.FC<EvaluationUpdatorV2Props> = ({
  subStep,
  evaluationId,
  onSubmit,
}) => {
  const { seek } = useGlobalAudioPlayer();
  const [comment, setComment] = useState<string>('');
  const [manualResult, setManualResult] = useState<string | undefined>();
  const { globalConfig } = useRole();
  const { permissions } = usePermission();
  return (
    <section className="w-full overflow-x-auto">
      <div className="w-full flex flex-col px-6 py-4 border border-[#EEEFF1] bg-[#FCFCFD] rounded-md gap-2">
        {/* Time & Notes */}
        <div className="flex gap-2">
          <div>
            <span className="text-[#949494]">Time: </span>
            <span
              className="underline cursor-pointer"
              onClick={() => {
                if (subStep.timerStart) {
                  console.log(
                    'clicked',
                    timeStringToSeconds(subStep.timerStart)
                  );
                  seek(timeStringToSeconds(subStep.timerStart));
                }
              }}
            >
              {subStep.timerStart + ' - ' + subStep.timerEnd}
            </span>
          </div>
          <div>
            <span className="text-[#949494]">Notes: </span>
            <span>{subStep.notes ? subStep.notes : ''}</span>
          </div>
        </div>
        {/* Override to */}
        {new CommonPermission(globalConfig, permissions).isPermissionEnabled(
          'ctint-mf-interaction',
          'qm',
          'edit'
        ) && (
          <div className="flex items-center">
            <span className="flex flex-col flex-1 gap-2 items-left">
              {/* manual result */}
              <div>
                <ToggleGroup.Root
                  type="single"
                  onValueChange={(value) => {
                    console.log('value', value);

                    if (value && value?.length > 0) setManualResult(value);
                    else setManualResult(undefined);
                  }}
                  className="flex-wrap"
                >
                  <ToggleGroup.Item
                    value={
                      subStep.autoResult === 'Passed' ? 'Failed' : 'Passed'
                    }
                    className="data-[state=on]:bg-black data-[state=on]:text-white font-light px-2 py-[2px] rounded-[0.25rem] border-[1px] border-[#E0E0E0]"
                  >
                    Overrided to:{' '}
                    {subStep.autoResult === 'Passed' ? 'Failed' : 'Passed'}
                  </ToggleGroup.Item>
                </ToggleGroup.Root>
              </div>
              {/* manual comment and submit button */}
              <div className="flex flex-1 border-[1px] border-[#E0E0E0] rounded-[0.25rem]">
                <input
                  type="text"
                  alt="comment"
                  placeholder={
                    manualResult
                      ? `Override to ${manualResult}, because...`
                      : 'Leave a comment'
                  }
                  value={comment}
                  className="flex-1 px-2 py-[2px] rounded-l-[0.25rem] focus:outline-none font-light"
                  onChange={(v) => setComment(v.target.value)}
                />
                <button
                  className="px-2 py-[2px] bg-black text-white rounded-r-[0.25rem] font-light"
                  onClick={() => onSubmit(comment, manualResult || '')}
                >
                  Submit
                </button>
              </div>
            </span>
          </div>
        )}
      </div>
    </section>
  );
};

export default EvaluationUpdatorV2;
