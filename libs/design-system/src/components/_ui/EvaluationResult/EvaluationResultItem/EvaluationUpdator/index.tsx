import { useState } from 'react';
import { EvaluationResultSubStep } from '../types/evaluation';
import * as ToggleGroup from '@radix-ui/react-toggle-group';
import { useGlobalAudioPlayer } from 'react-use-audio-player';
import { timeStringToSeconds } from '../../../../../lib/utils';
import { CommonPermission } from '../../../../../@types/CommonPermission';
import { useRole } from '../../../../../context/RoleContext';
import { usePermission } from '../../../../../context/PremissionContext';

interface EvaluationUpdatorProps {
  subStep: EvaluationResultSubStep;
  evaluationId: string;
  onSubmit: (comment: string, manualResult: string) => void;
}

const EvaluationUpdator: React.FC<EvaluationUpdatorProps> = ({
  subStep,
  evaluationId,
  onSubmit,
}) => {
  const { seek } = useGlobalAudioPlayer();
  const [comment, setComment] = useState<string>('');
  const [manualResult, setManualResult] = useState(subStep.manualResult);
  const { globalConfig } = useRole();
  const { permissions } = usePermission();
  return (
    <section className="w-full overflow-x-auto">
      <div className="w-full flex flex-col px-6 py-4 border border-[#EEEFF1] bg-[#FCFCFD] rounded-md gap-2">
        {/* Time & Notes */}
        <div className="flex gap-2">
          <div>
            <span className="text-[#949494]">Time: </span>
            <span
              className="underline cursor-pointer"
              onClick={() => {
                if (subStep.timerStart) {
                  console.log(
                    'clicked',
                    timeStringToSeconds(subStep.timerStart)
                  );
                  seek(timeStringToSeconds(subStep.timerStart));
                }
              }}
            >
              {subStep.timerStart + ' - ' + subStep.timerEnd}
            </span>
          </div>
          <div>
            <span className="text-[#949494]">Notes: </span>
            <span>{subStep.notes ? subStep.notes : ''}</span>
          </div>
        </div>
        {/* Override to */}
        {new CommonPermission(globalConfig, permissions).isPermissionEnabled(
          'ctint-mf-interaction',
          'qm',
          'edit'
        ) && (
          <div className="flex items-center">
            <span className="flex-wrap text-[#949494] mr-2">
              Overrided to:{' '}
            </span>
            <span className="flex flex-1 gap-2 items-center">
              {/* manual result */}
              <div>
                <ToggleGroup.Root
                  type="single"
                  value={manualResult}
                  onValueChange={(value) => {
                    if (value) setManualResult(value);
                  }}
                  className="flex-wrap border-[1px] border-[#E0E0E0] rounded-[0.25rem]"
                >
                  <ToggleGroup.Item
                    value="Passed"
                    className="data-[state=on]:bg-black data-[state=on]:text-white font-light px-2 py-[2px] rounded-l-[0.25rem] border-r-[1px] border-[#E0E0E0]"
                  >
                    Passed
                  </ToggleGroup.Item>
                  <ToggleGroup.Item
                    value="Failed"
                    className="data-[state=on]:bg-black data-[state=on]:text-white font-light px-2 py-[2px] rounded-r-[0.25rem]"
                  >
                    Failed
                  </ToggleGroup.Item>
                </ToggleGroup.Root>
              </div>
              {/* manual comment and submit button */}
              <div className="flex flex-1 border-[1px] border-[#E0E0E0] rounded-[0.25rem]">
                <input
                  type="text"
                  alt="comment"
                  disabled={!manualResult}
                  placeholder="because..."
                  value={comment}
                  className="flex-1 px-2 py-[2px] rounded-l-[0.25rem] focus:outline-none font-light"
                  onChange={(v) => setComment(v.target.value)}
                />
                <button
                  className="px-2 py-[2px] bg-black text-white rounded-r-[0.25rem] font-light"
                  disabled={
                    comment === subStep.manualComment &&
                    manualResult === subStep.manualResult
                  } // 未修改时不允许提交
                  onClick={() => onSubmit(comment, manualResult)}
                >
                  Submit
                </button>
              </div>
            </span>
          </div>
        )}
      </div>
    </section>
  );
};

export default EvaluationUpdator;
