import { ButtonHTMLAttributes, ReactNode, useEffect, useState } from 'react';
import { cn } from '../../../lib/utils';
import Icon from '../Icon';

type TSortOrder = 'asc' | 'desc' | false;

export type TSortingButtonProps = {
  testId?: string;
  sorting?: TSortOrder;
  onChangeSorting?: (sorting: TSortOrder) => void;
  children?: ReactNode;
  reactTableCol?: any;
} & ButtonHTMLAttributes<HTMLButtonElement>;

export const SortingButton = ({
  testId,
  sorting,
  children,
  onChangeSorting,
  reactTableCol,
  ...props
}: TSortingButtonProps) => {
  const [componentSortOrder, setComponentSortOrder] =
    useState<TSortOrder>(false);
  const sortOrder = sorting ?? componentSortOrder;
  const targetSortOrder = sortOrder === 'asc' ? 'desc' : 'asc';

  useEffect(() => {
    if (onChangeSorting) {
      onChangeSorting(sortOrder);
    }
  }, [onChangeSorting, sortOrder]);

  return (
    <button
      type="button"
      className={cn('group/sortingBtn', props.className)}
      data-testid={testId}
      onClick={() => {
        setComponentSortOrder(targetSortOrder);
      }}
      {...props}
    >
      <div className="inline-flex items-center gap-x-2">
        <div className="inline-flex">{children}</div>
        <div className="inline-flex flex-col gap-y-[3px] items-center text-grey-200">
          <div
            className={cn(
              'inline-flex rotate-180 origin-center -mt-[2px]',
              targetSortOrder === 'asc' &&
                'group-hover/sortingBtn:text-grey-500',
              sortOrder === 'asc' && 'text-black'
            )}
          >
            <Icon
              name="dropdown-arrow"
              size="0.5em"
            />
          </div>
          <div
            className={cn(
              'inline-flex origin-center',
              targetSortOrder === 'desc' &&
                'group-hover/sortingBtn:text-grey-500',
              sortOrder === 'desc' && 'text-black'
            )}
          >
            <Icon
              name="dropdown-arrow"
              size="0.5em"
            />
          </div>
        </div>
      </div>
    </button>
  );
};

export default SortingButton;
