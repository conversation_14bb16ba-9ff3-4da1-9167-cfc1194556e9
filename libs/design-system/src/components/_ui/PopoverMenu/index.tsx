import { FC } from 'react';
import * as Popover from '@radix-ui/react-popover';

interface IPopoverMenuProps {
  icon: React.ReactNode;
  children: React.ReactNode;
  side?: 'bottom' | 'top' | 'left' | 'right';
  align?: 'start' | 'center' | 'end';
}
const PopoverMenu: FC<IPopoverMenuProps> = ({
  icon,
  children,
  side = 'bottom', // default side is bottom
  align = 'end', // default align is end
}) => {
  return (
    <Popover.Root>
      <Popover.Trigger>{icon}</Popover.Trigger>
      <Popover.Portal>
        <Popover.Content
          side={side}
          align={align}
          className="mt-1 z-50"
        >
          {children}
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
};

export default PopoverMenu;
