import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '../../../components/_ui/DropdownMenu';
import { ReactNode } from 'react';

export type TDropdownBoxProps = {
  open: boolean;
  onOpenChange: () => void;
  trigger: ReactNode;
  children?: ReactNode;
  align?: 'center' | 'start' | 'end';
};

const DropdownBox: React.FC<TDropdownBoxProps> = ({
  open,
  onOpenChange,
  trigger,
  children,
  align = 'center',
}) => {
  return (
    <DropdownMenu
      open={open}
      onOpenChange={onOpenChange}
    >
      <DropdownMenuTrigger asChild>{trigger}</DropdownMenuTrigger>
      <DropdownMenuContent align={align}>{children}</DropdownMenuContent>
    </DropdownMenu>
  );
};

export default DropdownBox;
