import { cn } from '../../../lib/utils';
import { Tooltip } from '../Tooltip';

type TCallControlButtonProps = {
  icon: React.ReactNode;
  tooltip?: string;
  tooltipPosition?: 'top' | 'bottom' | 'left' | 'right' | undefined;
  label?: string;
  active?: boolean;
  className?: string;
  handleOnChange?: () => void;
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

const CallControlButton: React.FC<TCallControlButtonProps> = ({
  icon,
  tooltip,
  tooltipPosition = 'bottom',
  label,
  active,
  handleOnChange,
  className,
  ...props
}) => {
  const body = (
    <button
      disabled={props?.disabled}
      onClick={handleOnChange}
      className={cn(
        'flex flex-col justify-center items-center shadow-button-callButton call-buttons__button w-16 h-16 rounded-full bg-white hover:bg-primary-300 cursor-pointer disabled:cursor-not-allowed disabled:text-grey-300 disabled:border-none disabled:bg-white',
        active && 'bg-primary-500 shadow-button-primary hover:border-none',
        className
      )}
      {...props}
    >
      {icon}

      {label && (
        <span className="italic text-body call-buttons__label">{label}</span>
      )}
    </button>
  );

  if (!tooltip) return body;

  return (
    <Tooltip
      trigger={body}
      content={tooltip}
      side={tooltipPosition}
    />
  );
};

export default CallControlButton;
