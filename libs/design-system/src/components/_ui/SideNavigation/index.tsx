import Link from 'next/link';
import Icon from '../Icon';

const SideNavigation = () => {
  return (
    <nav className="relative h-screen w-16 bg-common-white flex flex-col justify-between items-start divide-y divide-grey-200 shadow-contact-window">
      <div className="w-16 px-2 py-3 flex items-center justify-center">
        <Link href="/">
          <Icon
            name="logo"
            size={40}
          />
        </Link>
      </div>
      <div className="w-full px-2 py-3 flex-1 flex flex-col justify-start items-center gap-4">
        {/* <Badge>
          <Avatar selected>AA</Avatar>
        </Badge>
        <Avatar>BB</Avatar>
        <Avatar>CC</Avatar> */}
      </div>
      {/* <div className="w-full px-2 py-3 flex items-center justify-center">
        <Icon
          name="saa"
          size={40}
        />
      </div> */}
    </nav>
  );
};

export default SideNavigation;
