interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconQMDelete: React.FC<IconProps> = ({
  size,
  alt,
  className,
  color = 'white',
}) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 15 15"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.4127 4.95845C12.6932 4.95845 12.9124 5.20202 12.8873 5.48331L12.8865 5.49184L12.1505 12.4111C12.0552 13.3074 11.3125 13.9884 10.424 13.9999L10.397 14L5.77435 13.9889C4.88663 13.9867 4.14107 13.3166 4.03244 12.4285L4.02935 12.4015L3.28843 5.49225C3.25817 5.21008 3.47428 4.96341 3.75369 4.9585L3.76217 4.95843H12.4127V4.95845ZM6.76004 7.04112C6.4995 7.04112 6.2878 7.25256 6.2836 7.51501L6.28353 7.52299V10.8219L6.28366 10.8333C6.28964 11.0942 6.50063 11.3038 6.76004 11.3038C7.02058 11.3038 7.23228 11.0924 7.23649 10.8299L7.23655 10.8219V7.52299L7.23642 7.51161C7.23045 7.25074 7.01945 7.04112 6.76004 7.04112ZM9.24616 7.04112C8.98562 7.04112 8.77392 7.25256 8.76971 7.51501L8.76965 7.52299V10.8219L8.76978 10.8333C8.77575 11.0942 8.98675 11.3038 9.24616 11.3038C9.5067 11.3038 9.7184 11.0924 9.7226 10.8299L9.72267 10.8219V7.52299L9.72254 7.51161C9.71656 7.25074 9.50557 7.04112 9.24616 7.04112ZM13.5235 3.6561C13.7867 3.6561 14 3.87184 14 4.13797C14 4.40029 13.7927 4.61367 13.5347 4.61971L13.5235 4.61984H2.47651C2.21334 4.61984 2 4.40409 2 4.13797C2 3.87564 2.20728 3.66227 2.46526 3.65623L2.47651 3.6561H13.5235ZM9.87256 2C10.1357 2 10.3491 2.21574 10.3491 2.48187C10.3491 2.74419 10.1418 2.95757 9.88381 2.96361L9.87256 2.96374H6.12364C5.86047 2.96374 5.64713 2.748 5.64713 2.48187C5.64713 2.21954 5.85442 2.00617 6.11239 2.00013L6.12364 2H9.87256Z"
        fill={color}
      />
    </svg>
  );
};

export default IconQMDelete;
