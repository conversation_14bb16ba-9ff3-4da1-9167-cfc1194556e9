interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconQMPublish: React.FC<IconProps> = ({
  size,
  alt,
  className,
  color = 'white',
}) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 15 15"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.6821 11.1367L6.71338 9.60742L11.6821 3.87402L5.18408 9.60742L1.3623 8.07812L13.5938 1.58008L11.6821 11.1367ZM6.71338 13.4307V10.7544L8.24268 11.519L6.71338 13.4307Z"
        fill={color}
      />
    </svg>
  );
};

export default IconQMPublish;
