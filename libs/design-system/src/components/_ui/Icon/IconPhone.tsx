import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconPhone: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 20 20"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_2770_2945)">
        <path
          d="M6.44141 0.960842C6.14062 0.234279 5.34766 -0.15244 4.58984 0.0545917L1.15234 0.992092C0.472656 1.17959 0 1.79678 0 2.4999C0 12.164 7.83594 19.9999 17.5 19.9999C18.2031 19.9999 18.8203 19.5272 19.0078 18.8476L19.9453 15.4101C20.1523 14.6522 19.7656 13.8593 19.0391 13.5585L15.2891 11.996C14.6523 11.7304 13.9141 11.914 13.4805 12.4491L11.9023 14.3749C9.15234 13.0741 6.92578 10.8476 5.625 8.09756L7.55078 6.52334C8.08594 6.08584 8.26953 5.35147 8.00391 4.71475L6.44141 0.964748V0.960842Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2770_2945">
          <rect
            width="20"
            height="20"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconPhone;
