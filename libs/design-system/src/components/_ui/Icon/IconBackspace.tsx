import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconBackspace: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 32 32"
      fill="none"
      aria-label={alt}
    >
      <g id="icon / Backspace">
        <path
          id="Vector"
          d="M28.5143 5.12013H9.80573C9.29861 5.11856 8.79732 5.22748 8.33722 5.43918C7.87712 5.65089 7.46942 5.96024 7.14288 6.34541L0.822881 13.7651C0.291484 14.3904 0 15.1821 0 16.0001C0 16.8182 0.291484 17.6099 0.822881 18.2351L7.14288 25.6548C7.46942 26.04 7.87712 26.3493 8.33722 26.5611C8.79732 26.7728 9.29861 26.8817 9.80573 26.8801H28.5143C29.4382 26.8783 30.3237 26.5132 30.9771 25.8646C31.6304 25.2161 31.9982 24.337 32 23.4198V8.5804C31.9982 7.66323 31.6304 6.78415 30.9771 6.13561C30.3237 5.48707 29.4382 5.12193 28.5143 5.12013ZM23.1886 18.9045C23.4027 19.1187 23.5228 19.4082 23.5228 19.71C23.5228 20.0117 23.4027 20.3013 23.1886 20.5155C22.9712 20.7254 22.6803 20.8433 22.3771 20.8445C22.0744 20.8412 21.7842 20.7236 21.5657 20.5155L18.64 17.6111L15.7143 20.5155C15.6077 20.6205 15.4814 20.7037 15.3424 20.7602C15.2035 20.8168 15.0547 20.8456 14.9045 20.8451C14.7544 20.8446 14.6058 20.8147 14.4673 20.7572C14.3287 20.6996 14.203 20.6156 14.0972 20.5098C13.9913 20.404 13.9076 20.2786 13.8506 20.1407C13.7936 20.0027 13.7646 19.855 13.7651 19.706C13.7656 19.5569 13.7957 19.4094 13.8537 19.2719C13.9116 19.1343 13.9963 19.0095 14.1029 18.9045L17.0286 16.0001L14.1029 13.0958C13.8888 12.8816 13.7686 12.592 13.7686 12.2903C13.7686 11.9885 13.8888 11.699 14.1029 11.4848C14.2086 11.3796 14.3342 11.2961 14.4724 11.2392C14.6107 11.1823 14.7589 11.153 14.9086 11.153C15.0583 11.153 15.2065 11.1823 15.3447 11.2392C15.483 11.2961 15.6086 11.3796 15.7143 11.4848L18.64 14.3891L21.5657 11.4848C21.6723 11.379 21.7988 11.2951 21.938 11.2378C22.0772 11.1806 22.2265 11.1511 22.3771 11.1511C22.5278 11.1511 22.6771 11.1806 22.8163 11.2378C22.9555 11.2951 23.082 11.379 23.1886 11.4848C23.2951 11.5905 23.3797 11.7161 23.4373 11.8543C23.495 11.9925 23.5247 12.1407 23.5247 12.2903C23.5247 12.4399 23.495 12.588 23.4373 12.7262C23.3797 12.8644 23.2951 12.99 23.1886 13.0958L20.2629 16.0001L23.1886 18.9045Z"
          fill="black"
        />
      </g>
    </svg>
  );
};

export default IconBackspace;
