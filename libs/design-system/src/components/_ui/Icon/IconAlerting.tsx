import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconAlerting: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 14 14"
      fill="none"
    >
      <path
        d="M4.14814 1.09762C3.42014 0.91282 2.65714 0.99122 2.05934 1.32582C1.45034 1.66602 1.02334 2.26942 1.00234 3.07652C0.970137 4.33792 1.26204 6.03052 2.42124 8.01712C3.56574 9.97922 4.82924 11.1552 5.87784 11.8538C6.54004 12.2955 7.27154 12.3074 7.89664 12.0197C8.51264 11.7362 9.01244 11.1699 9.27214 10.4678C9.33668 10.2932 9.36259 10.1066 9.34809 9.92101C9.33359 9.73539 9.27902 9.55512 9.18814 9.39262L8.52034 8.20122C8.31506 7.83454 7.98468 7.55397 7.58959 7.41079C7.1945 7.26761 6.76108 7.27138 6.36854 7.42142L5.90234 7.59992C5.67624 7.68602 5.46834 7.64612 5.35074 7.52992C4.95524 7.14142 4.67244 6.61432 4.54434 6.04592C4.50374 5.86672 4.58634 5.65532 4.78374 5.50622L5.20444 5.18842C5.54455 4.93178 5.77934 4.55986 5.86479 4.14244C5.95023 3.72502 5.88045 3.29076 5.66854 2.92112L4.98814 1.73462C4.89797 1.57756 4.77714 1.44024 4.63283 1.33081C4.48853 1.22138 4.3237 1.14208 4.14814 1.09762ZM1.70234 3.09542C1.71634 2.55362 1.99074 2.16652 2.40094 1.93692C2.82234 1.70102 3.39774 1.62892 3.97594 1.77592C4.06066 1.79732 4.14021 1.83555 4.20984 1.88835C4.27947 1.94114 4.33776 2.00742 4.38124 2.08322L5.06094 3.26972C5.18807 3.49141 5.22998 3.75186 5.17882 4.00225C5.12766 4.25263 4.98694 4.47576 4.78304 4.62982L4.36234 4.94762C3.99834 5.22202 3.74634 5.69242 3.86114 6.19992C4.01654 6.88732 4.36024 7.53902 4.86004 8.02972C5.22544 8.38812 5.74624 8.40912 6.15224 8.25372L6.61844 8.07522C6.85398 7.98506 7.11411 7.98269 7.35125 8.06855C7.58839 8.15441 7.78671 8.32276 7.90994 8.54282L8.57704 9.73492C8.66104 9.88472 8.67504 10.0639 8.61554 10.2249C8.41254 10.7744 8.03104 11.1867 7.60404 11.3834C7.18614 11.5759 6.71364 11.5703 6.26634 11.2714C5.30664 10.6316 4.11734 9.53472 3.02534 7.66432C1.93264 5.78832 1.67294 4.22452 1.70234 3.09542ZM8.49444 1.05982C8.45427 1.03748 8.41009 1.02328 8.36443 1.01801C8.31876 1.01275 8.27251 1.01653 8.22831 1.02914C8.18411 1.04175 8.14283 1.06294 8.10682 1.09151C8.07081 1.12007 8.04077 1.15545 8.01844 1.19562C7.9961 1.23579 7.98189 1.27997 7.97663 1.32563C7.97136 1.37129 7.97514 1.41754 7.98775 1.46175C8.00036 1.50595 8.02156 1.54723 8.05012 1.58324C8.07869 1.61925 8.11407 1.64928 8.15424 1.67162L8.42304 1.82142C9.26276 2.2879 9.97707 2.95076 10.5049 3.75335C11.0327 4.55594 11.3584 5.4744 11.454 6.43022L11.4764 6.65072C11.4791 6.69792 11.4913 6.74409 11.5122 6.78645C11.5332 6.82881 11.5625 6.86648 11.5985 6.8972C11.6344 6.92791 11.6762 6.95103 11.7213 6.96516C11.7664 6.97928 11.8139 6.98413 11.8609 6.97939C11.908 6.97466 11.9535 6.96045 11.9949 6.93762C12.0363 6.9148 12.0727 6.88382 12.1017 6.84656C12.1308 6.80931 12.1521 6.76655 12.1642 6.72086C12.1763 6.67517 12.179 6.6275 12.1722 6.58072L12.1505 6.36092C12.0437 5.29243 11.6798 4.2657 11.0898 3.3685C10.4998 2.47131 9.70126 1.73033 8.76254 1.20892L8.49444 1.05982ZM7.67404 2.93582C7.72185 2.85628 7.7993 2.79899 7.88935 2.77654C7.9794 2.75409 8.07468 2.76833 8.15424 2.81612L8.24104 2.86792C8.80595 3.20676 9.29055 3.66422 9.66134 4.20869C10.0321 4.75316 10.2803 5.37165 10.3886 6.02142L10.4194 6.20832C10.4282 6.25415 10.4278 6.30128 10.4181 6.34694C10.4084 6.3926 10.3897 6.43586 10.3631 6.47418C10.3364 6.5125 10.3024 6.5451 10.263 6.57007C10.2236 6.59504 10.1795 6.61187 10.1335 6.61958C10.0875 6.62728 10.0404 6.6257 9.99494 6.61493C9.94953 6.60415 9.90673 6.58441 9.86907 6.55685C9.8314 6.52929 9.79963 6.49448 9.77561 6.45446C9.7516 6.41444 9.73583 6.37002 9.72924 6.32382L9.69774 6.13692C9.60614 5.58717 9.39628 5.06386 9.08265 4.60315C8.76903 4.14244 8.35911 3.75531 7.88124 3.46852L7.79444 3.41602C7.755 3.3924 7.72059 3.36124 7.69319 3.32433C7.6658 3.28741 7.64594 3.24545 7.63476 3.20086C7.62358 3.15627 7.62129 3.10991 7.62803 3.06443C7.63477 3.01896 7.6504 2.97525 7.67404 2.93582Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default IconAlerting;
