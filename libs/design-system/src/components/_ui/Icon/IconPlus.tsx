import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconPlus: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 21 21"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.66 20.0455C9.66 20.5726 10.1301 21 10.71 21C11.2899 21 11.76 20.5726 11.76 20.0455V11.76H20.0455C20.5726 11.76 21 11.2899 21 10.71C21 10.1301 20.5726 9.66 20.0455 9.66H11.76V0.954546C11.76 0.427365 11.2899 0 10.71 0C10.1301 0 9.66 0.427365 9.66 0.954546V9.66H0.954545C0.427365 9.66 0 10.1301 0 10.71C0 11.2899 0.427365 11.76 0.954545 11.76H9.66V20.0455Z"
        fill="black"
      />
    </svg>
  );
};

export default IconPlus;
