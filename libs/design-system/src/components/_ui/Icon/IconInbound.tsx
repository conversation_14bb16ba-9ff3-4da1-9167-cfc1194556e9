import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconInbound: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 24 24"
    >
      <g clipPath="url(#clip0_2767_2598)">
        <path
          d="M6.18348 5.72329C5.89474 5.02582 5.13352 4.65459 4.40606 4.85333L1.1062 5.75329C0.45373 5.93328 0 6.52576 0 7.20073C0 16.4778 7.52217 24 16.7993 24C17.4742 24 18.0667 23.5463 18.2467 22.8938L19.1467 19.5939C19.3454 18.8665 18.9742 18.1052 18.2767 17.8165L14.6769 16.3166C14.0656 16.0616 13.3569 16.2378 12.9407 16.7516L11.4257 18.6002C8.78586 17.3515 6.64846 15.2141 5.39976 12.5742L7.24843 11.0631C7.76216 10.6431 7.9384 9.93811 7.68341 9.32688L6.18348 5.72704V5.72329Z"
          fill="black"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.0603 3.41169C12.5145 3.40877 12.0708 3.85245 12.0708 4.4012L12.0737 10.9425C12.0708 11.4883 12.5145 11.932 13.0632 11.932L19.5987 11.9291C20.1445 11.932 20.5853 11.4854 20.5882 10.9396C20.5911 10.3937 20.1474 9.95005 19.5987 9.95005H15.4538L20.7692 4.63472C21.1574 4.2465 21.1574 3.62185 20.7692 3.23364C20.3809 2.84542 19.7563 2.84542 19.3681 3.23364L14.0498 8.54606L14.0498 4.4012C14.0527 3.85537 13.6062 3.41461 13.0603 3.41169Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_2767_2598">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconInbound;
