import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconCalendar: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 20 21"
    >
      <path
        d="M4.74266 2.45005V3.60005H2.77123C1.68284 3.60005 0.799805 4.37271 0.799805 5.32505V7.05005H19.1998V5.32505C19.1998 4.37271 18.3168 3.60005 17.2284 3.60005H15.2569V2.45005C15.2569 1.81396 14.6696 1.30005 13.9427 1.30005C13.2157 1.30005 12.6284 1.81396 12.6284 2.45005V3.60005H7.37123V2.45005C7.37123 1.81396 6.78391 1.30005 6.05695 1.30005C5.32998 1.30005 4.74266 1.81396 4.74266 2.45005ZM19.1998 8.20005H0.799805V17.9751C0.799805 18.9274 1.68284 19.7001 2.77123 19.7001H17.2284C18.3168 19.7001 19.1998 18.9274 19.1998 17.9751V8.20005Z"
        fill="black"
      />
    </svg>
  );
};

export default IconCalendar;
