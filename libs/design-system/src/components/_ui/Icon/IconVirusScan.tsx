import React from 'react';
import IconStatusQueued from '@cdss-modules/design-system/components/_ui/Icon/IconStatusQueued';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  status:
    | 'piiSuccess'
    | 'piiFailed'
    | 'piiNoMask'
    | 'success'
    | 'failed'
    | `detected`
    | 'scanning'
    | 'processing'
    | 'reason'
    | '';
}

const IconVirusScan: React.FC<IconProps> = ({
  size = '20px',
  alt = '',
  className = '',
  status,
}) => {
  // If status is empty, return null (no icon)
  if (status === '') {
    return (
      <svg
        className={`${className}`}
        style={{ width: size, height: size }}
        viewBox="0 0 12 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clip-path="url(#clip0_5401_135581)">
          <path
            d="M6 12C7.5913 12 9.11742 11.3679 10.2426 10.2426C11.3679 9.11742 12 7.5913 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 0 4.4087 0 6C0 7.5913 0.632141 9.11742 1.75736 10.2426C2.88258 11.3679 4.4087 12 6 12Z"
            fill="white"
          />
          <path
            d="M6 12C7.5913 12 9.11742 11.3679 10.2426 10.2426C11.3679 9.11742 12 7.5913 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 0 4.4087 0 6C0 7.5913 0.632141 9.11742 1.75736 10.2426C2.88258 11.3679 4.4087 12 6 12ZM4.10156 4.10156C4.32188 3.88125 4.67812 3.88125 4.89609 4.10156L5.99766 5.20312L7.09922 4.10156C7.31953 3.88125 7.67578 3.88125 7.89375 4.10156C8.11172 4.32188 8.11406 4.67812 7.89375 4.89609L6.79219 5.99766L7.89375 7.09922C8.11406 7.31953 8.11406 7.67578 7.89375 7.89375C7.67344 8.11172 7.31719 8.11406 7.09922 7.89375L5.99766 6.79219L4.89609 7.89375C4.67578 8.11406 4.31953 8.11406 4.10156 7.89375C3.88359 7.67344 3.88125 7.31719 4.10156 7.09922L5.20312 5.99766L4.10156 4.89609C3.88125 4.67578 3.88125 4.31953 4.10156 4.10156Z"
            fill="#949494"
          />
        </g>
        <defs>
          <clipPath id="clip0_5401_135581">
            <rect
              width="12"
              height="12"
              fill="white"
            />
          </clipPath>
        </defs>
      </svg>
    );
  }

  // 根据状态返回不同的图标
  switch (status) {
    case 'piiSuccess':
      return (
        <svg
          className={`${className}`}
          style={{ width: size, height: size }}
          viewBox="0 0 12 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clip-path="url(#clip0_5313_105443)">
            <path
              d="M6 12C7.5913 12 9.11742 11.3679 10.2426 10.2426C11.3679 9.11742 12 7.5913 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 0 4.4087 0 6C0 7.5913 0.632141 9.11742 1.75736 10.2426C2.88258 11.3679 4.4087 12 6 12Z"
              fill="#1CC500"
            />
            <rect
              x="3.12012"
              y="6.37793"
              width="1.2"
              height="2.98183"
              rx="0.6"
              transform="rotate(-47.1262 3.12012 6.37793)"
              fill="white"
            />
            <rect
              x="8.15259"
              y="3.6001"
              width="1.2"
              height="5.45666"
              rx="0.6"
              transform="rotate(42.8951 8.15259 3.6001)"
              fill="white"
            />
          </g>
          <defs>
            <clipPath id="clip0_5313_105443">
              <rect
                width="12"
                height="12"
                fill="white"
              />
            </clipPath>
          </defs>
        </svg>
      );
    case 'piiNoMask':
      return (
        <svg
          className={`${className}`}
          style={{ width: size, height: size }}
          viewBox="0 0 12 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clip-path="url(#clip0_5313_105443)">
            <path
              d="M6 12C7.5913 12 9.11742 11.3679 10.2426 10.2426C11.3679 9.11742 12 7.5913 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 0 4.4087 0 6C0 7.5913 0.632141 9.11742 1.75736 10.2426C2.88258 11.3679 4.4087 12 6 12Z"
              fill="#1CC500"
            />
            <rect
              x="3.12012"
              y="6.37793"
              width="1.2"
              height="2.98183"
              rx="0.6"
              transform="rotate(-47.1262 3.12012 6.37793)"
              fill="white"
            />
            <rect
              x="8.15259"
              y="3.6001"
              width="1.2"
              height="5.45666"
              rx="0.6"
              transform="rotate(42.8951 8.15259 3.6001)"
              fill="white"
            />
          </g>
          <defs>
            <clipPath id="clip0_5313_105443">
              <rect
                width="12"
                height="12"
                fill="white"
              />
            </clipPath>
          </defs>
        </svg>
      );
    case 'piiFailed':
      return (
        <svg
          className={`${className}`}
          style={{ width: size, height: size }}
          viewBox="0 0 12 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clip-path="url(#clip0_5345_115118)">
            <path
              d="M6 12C7.5913 12 9.11742 11.3679 10.2426 10.2426C11.3679 9.11742 12 7.5913 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 0 4.4087 0 6C0 7.5913 0.632141 9.11742 1.75736 10.2426C2.88258 11.3679 4.4087 12 6 12Z"
              fill="white"
            />
            <path
              d="M6 12C7.5913 12 9.11742 11.3679 10.2426 10.2426C11.3679 9.11742 12 7.5913 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 0 4.4087 0 6C0 7.5913 0.632141 9.11742 1.75736 10.2426C2.88258 11.3679 4.4087 12 6 12ZM4.10156 4.10156C4.32188 3.88125 4.67812 3.88125 4.89609 4.10156L5.99766 5.20312L7.09922 4.10156C7.31953 3.88125 7.67578 3.88125 7.89375 4.10156C8.11172 4.32188 8.11406 4.67812 7.89375 4.89609L6.79219 5.99766L7.89375 7.09922C8.11406 7.31953 8.11406 7.67578 7.89375 7.89375C7.67344 8.11172 7.31719 8.11406 7.09922 7.89375L5.99766 6.79219L4.89609 7.89375C4.67578 8.11406 4.31953 8.11406 4.10156 7.89375C3.88359 7.67344 3.88125 7.31719 4.10156 7.09922L5.20312 5.99766L4.10156 4.89609C3.88125 4.67578 3.88125 4.31953 4.10156 4.10156Z"
              fill="#E65300"
            />
          </g>
          <defs>
            <clipPath id="clip0_5345_115118">
              <rect
                width="12"
                height="12"
                fill="white"
              />
            </clipPath>
          </defs>
        </svg>
      );
    case 'success':
      return (
        <svg
          className={`${className}`}
          style={{ width: size, height: size }}
          viewBox="0 0 14 14"
          aria-label={alt}
        >
          <path
            d="M11.8986 2.6035C11.7518 2.47021 11.5585 2.39728 11.3585 2.39728C11.3403 2.39728 11.3221 2.39728 11.3039 2.39854C11.2929 2.39979 11.1655 2.40737 10.9666 2.40737C10.6369 2.40737 9.99314 2.38473 9.38335 2.24015C8.59598 2.05281 7.68727 1.18524 7.4229 1.01425C7.28963 0.927521 7.13624 0.884766 6.98254 0.884766C6.82947 0.884766 6.67607 0.927521 6.54311 1.013C6.51105 1.03438 5.55957 2.01884 4.62888 2.24015C4.01941 2.38473 3.36341 2.40737 3.03399 2.40737C2.83502 2.40737 2.70805 2.39979 2.69549 2.39854C2.6782 2.39728 2.6609 2.39728 2.6433 2.39728C2.44307 2.39728 2.24913 2.4702 2.10172 2.6035C1.94173 2.74808 1.85059 2.9505 1.85059 3.16298V5.07784C1.85059 12.1564 6.63964 13.068 6.84267 13.1044C6.8892 13.112 6.93603 13.1157 6.98287 13.1157C7.02939 13.1157 7.07685 13.112 7.12273 13.1044C7.32609 13.068 12.1503 12.1564 12.1503 5.07784V3.16298C12.1503 2.9505 12.0589 2.74809 11.8986 2.6035ZM10.1283 5.34187L6.8081 8.55429C6.78735 8.5882 6.76189 8.6222 6.73108 8.6511C6.63364 8.7454 6.50477 8.79066 6.37714 8.78814C6.24954 8.79066 6.12096 8.7454 6.02354 8.6511C5.99275 8.6222 5.96698 8.5882 5.94623 8.55429L4.17188 6.83681C3.98295 6.65449 3.98295 6.35905 4.17188 6.17547C4.36078 5.9932 4.66723 5.9932 4.85616 6.17547L6.37715 7.64779L9.44434 4.68058C9.63327 4.49825 9.93939 4.49825 10.1283 4.68058C10.3172 4.86283 10.3172 5.15959 10.1283 5.34187Z"
            fill="#57B62D"
          />
        </svg>
      );
    case 'failed':
      return (
        <svg
          className={`${className}`}
          style={{ width: size, height: size }}
          viewBox="0 0 14 14"
          aria-label={alt}
        >
          <g clipPath="url(#clip0_1610_28392)">
            <path
              d="M11.8986 2.6035C11.7518 2.47021 11.5585 2.39728 11.3585 2.39728C11.3403 2.39728 11.3221 2.39728 11.3039 2.39854C11.2929 2.39979 11.1655 2.40737 10.9666 2.40737C10.6369 2.40737 9.99314 2.38473 9.38335 2.24015C8.59598 2.05281 7.68727 1.18524 7.4229 1.01425C7.28963 0.927521 7.13624 0.884766 6.98254 0.884766C6.82947 0.884766 6.67607 0.927521 6.54311 1.013C6.51105 1.03438 5.55957 2.01884 4.62888 2.24015C4.01941 2.38473 3.36341 2.40737 3.03399 2.40737C2.83502 2.40737 2.70805 2.39979 2.69549 2.39854C2.6782 2.39728 2.6609 2.39728 2.6433 2.39728C2.44307 2.39728 2.24913 2.4702 2.10172 2.6035C1.94173 2.74808 1.85059 2.9505 1.85059 3.16298V5.07784C1.85059 12.1564 6.63964 13.068 6.84267 13.1044C6.8892 13.112 6.93603 13.1157 6.98287 13.1157C7.02939 13.1157 7.07685 13.112 7.12273 13.1044C7.32609 13.068 12.1503 12.1564 12.1503 5.07784V3.16298C12.1503 2.9505 12.0589 2.74809 11.8986 2.6035Z"
              fill="#FF271C"
            />
            <path
              d="M7.36645 7.00004L8.92399 5.44243C9.02534 5.34111 9.02534 5.17729 8.92399 5.07599C8.82267 4.97467 8.65885 4.97467 8.55755 5.07599L6.99997 6.63358L5.44246 5.07599C5.34109 4.97467 5.17734 4.97467 5.07602 5.07599C4.97466 5.17731 4.97466 5.34111 5.07602 5.44243L6.63353 7.00004L5.07603 8.55763C4.97467 8.65895 4.97467 8.82277 5.07603 8.92407C5.10007 8.94817 5.12862 8.96729 5.16007 8.98032C5.19151 8.99335 5.22522 9.00004 5.25925 9C5.32558 9 5.39193 8.97461 5.44247 8.92407L6.99997 7.36648L8.55755 8.92407C8.58159 8.94817 8.61015 8.96728 8.64159 8.98031C8.67303 8.99334 8.70673 9.00003 8.74077 9C8.8071 9 8.87345 8.97461 8.92399 8.92407C9.02534 8.82276 9.02534 8.65895 8.92399 8.55763L7.36645 7.00004Z"
              fill="white"
            />
          </g>
          <defs>
            <clipPath id="clip0_1610_28392">
              <rect
                width="14"
                height="14"
                fill="white"
              />
            </clipPath>
          </defs>
        </svg>
      );
    case 'detected':
      return (
        <svg
          className={`${className}`}
          style={{ width: size, height: size }}
          viewBox="0 0 14 14"
          aria-label={alt}
        >
          <g clipPath="url(#clip0_1610_28392)">
            <path
              d="M11.8986 2.6035C11.7518 2.47021 11.5585 2.39728 11.3585 2.39728C11.3403 2.39728 11.3221 2.39728 11.3039 2.39854C11.2929 2.39979 11.1655 2.40737 10.9666 2.40737C10.6369 2.40737 9.99314 2.38473 9.38335 2.24015C8.59598 2.05281 7.68727 1.18524 7.4229 1.01425C7.28963 0.927521 7.13624 0.884766 6.98254 0.884766C6.82947 0.884766 6.67607 0.927521 6.54311 1.013C6.51105 1.03438 5.55957 2.01884 4.62888 2.24015C4.01941 2.38473 3.36341 2.40737 3.03399 2.40737C2.83502 2.40737 2.70805 2.39979 2.69549 2.39854C2.6782 2.39728 2.6609 2.39728 2.6433 2.39728C2.44307 2.39728 2.24913 2.4702 2.10172 2.6035C1.94173 2.74808 1.85059 2.9505 1.85059 3.16298V5.07784C1.85059 12.1564 6.63964 13.068 6.84267 13.1044C6.8892 13.112 6.93603 13.1157 6.98287 13.1157C7.02939 13.1157 7.07685 13.112 7.12273 13.1044C7.32609 13.068 12.1503 12.1564 12.1503 5.07784V3.16298C12.1503 2.9505 12.0589 2.74809 11.8986 2.6035Z"
              fill="#FF271C"
            />
            <path
              d="M7.36645 7.00004L8.92399 5.44243C9.02534 5.34111 9.02534 5.17729 8.92399 5.07599C8.82267 4.97467 8.65885 4.97467 8.55755 5.07599L6.99997 6.63358L5.44246 5.07599C5.34109 4.97467 5.17734 4.97467 5.07602 5.07599C4.97466 5.17731 4.97466 5.34111 5.07602 5.44243L6.63353 7.00004L5.07603 8.55763C4.97467 8.65895 4.97467 8.82277 5.07603 8.92407C5.10007 8.94817 5.12862 8.96729 5.16007 8.98032C5.19151 8.99335 5.22522 9.00004 5.25925 9C5.32558 9 5.39193 8.97461 5.44247 8.92407L6.99997 7.36648L8.55755 8.92407C8.58159 8.94817 8.61015 8.96728 8.64159 8.98031C8.67303 8.99334 8.70673 9.00003 8.74077 9C8.8071 9 8.87345 8.97461 8.92399 8.92407C9.02534 8.82276 9.02534 8.65895 8.92399 8.55763L7.36645 7.00004Z"
              fill="white"
            />
          </g>
          <defs>
            <clipPath id="clip0_1610_28392">
              <rect
                width="14"
                height="14"
                fill="white"
              />
            </clipPath>
          </defs>
        </svg>
      );
    case 'reason':
      return (
        <svg
          className={`${className}`}
          style={{ width: size, height: size }}
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8 14.25C11.4518 14.25 14.25 11.4518 14.25 8C14.25 4.54822 11.4518 1.75 8 1.75C4.54822 1.75 1.75 4.54822 1.75 8C1.75 11.4518 4.54822 14.25 8 14.25Z"
            stroke="#949494"
            stroke-width="1.3"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M8 5.5V8"
            stroke="#949494"
            stroke-width="1.3"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M8 10.5H8.00625"
            stroke="#949494"
            stroke-width="1.3"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      );
    case 'scanning':
      return (
        <IconStatusQueued
          size={size}
          className={'animate-spin '}
        />
      );
    case 'processing':
      return (
        <IconStatusQueued
          size={size}
          className={'animate-spin '}
        />
      );
    default:
      return null;
  }
};

export default IconVirusScan;
