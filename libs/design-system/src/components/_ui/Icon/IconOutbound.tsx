import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconOutbound: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 50 50"
      fill="none"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_3279_6453)">
        <path
          d="M12.8822 11.9236C12.2807 10.4705 10.6948 9.69709 9.17928 10.1111L2.30459 11.986C0.945271 12.361 0 13.5954 0 15.0015C0 34.3288 15.6712 50 34.9985 50C36.4046 50 37.639 49.0547 38.014 47.6954L39.8889 40.8207C40.3029 39.3052 39.5295 37.7193 38.0764 37.1178L30.5768 33.9929C29.3034 33.4617 27.8269 33.8288 26.9597 34.8991L23.8036 38.7505C18.3039 36.149 13.851 31.6961 11.2495 26.1964L15.1009 23.0481C16.1712 22.1731 16.5383 20.7044 16.0071 19.431L12.8822 11.9314V11.9236Z"
          fill="currentColor"
        />
        <path
          d="M50 18.4301C50 19.773 48.9142 20.8588 47.5784 20.8517C46.2426 20.8446 45.1496 19.7659 45.1568 18.4301V8.28639L32.1414 21.2874C31.1913 22.2375 29.6626 22.2375 28.7126 21.2874C27.7625 20.3374 27.7625 18.8087 28.7126 17.8586L41.7208 4.85039H31.5771C30.2341 4.85039 29.1483 3.76459 29.1554 2.42877C29.1626 1.09294 30.2413 -1.23789e-06 31.5771 0.00714211L47.5712 0C48.9142 0 50 1.0858 49.9929 2.42163L50 18.4301Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_3279_6453">
          <rect
            width="50"
            height="50"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconOutbound;
