import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconVerticalDots: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 23 23"
    >
      <path
        d="M11.5719 20.7C10.9428 20.7 10.3395 20.4487 9.89472 20.0014C9.44991 19.5541 9.20001 18.9474 9.20001 18.3148C9.20001 17.6822 9.44991 17.0756 9.89472 16.6282C10.3395 16.1809 10.9428 15.9296 11.5719 15.9296C12.2009 15.9296 12.8042 16.1809 13.2491 16.6282C13.6939 17.0756 13.9438 17.6822 13.9438 18.3148C13.9438 18.9474 13.6939 19.5541 13.2491 20.0014C12.8042 20.4487 12.2009 20.7 11.5719 20.7ZM11.5719 13.8852C10.9428 13.8852 10.3395 13.6339 9.89472 13.1866C9.44991 12.7393 9.20001 12.1326 9.20001 11.5C9.20001 10.8674 9.44991 10.2607 9.89472 9.81343C10.3395 9.36612 10.9428 9.11483 11.5719 9.11483C12.2009 9.11483 12.8042 9.36612 13.2491 9.81343C13.6939 10.2607 13.9438 10.8674 13.9438 11.5C13.9438 12.1326 13.6939 12.7393 13.2491 13.1866C12.8042 13.6339 12.2009 13.8852 11.5719 13.8852ZM9.20001 4.6852C9.20001 4.05261 9.44991 3.44592 9.89472 2.99862C10.3395 2.55131 10.9428 2.30001 11.5719 2.30001C12.2009 2.30001 12.8042 2.55131 13.2491 2.99862C13.6939 3.44592 13.9438 4.05261 13.9438 4.6852C13.9438 5.31779 13.6939 5.92447 13.2491 6.37178C12.8042 6.81909 12.2009 7.07038 11.5719 7.07038C10.9428 7.07038 10.3395 6.81909 9.89472 6.37178C9.44991 5.92447 9.20001 5.31779 9.20001 4.6852Z"
        fill="black"
      />
    </svg>
  );
};

export default IconVerticalDots;
