import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconCheck: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 11 11"
      aria-label={alt}
    >
      <path
        d="M10.7698 1.79226C11.0767 2.0999 11.0767 2.59952 10.7698 2.90717L4.48482 9.20776C4.17794 9.51541 3.67956 9.51541 3.37267 9.20776L0.230164 6.05747C-0.0767214 5.74982 -0.0767214 5.2502 0.230164 4.94255C0.537049 4.63491 1.03543 4.63491 1.34232 4.94255L3.92997 7.53417L9.66014 1.79226C9.96702 1.48461 10.4654 1.48461 10.7723 1.79226H10.7698Z"
        // fill="black"
      />
    </svg>
  );
};

export default IconCheck;
