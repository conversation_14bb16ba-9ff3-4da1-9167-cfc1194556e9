interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconProfile: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-none ${className}`}
      width={size}
      height={size}
      viewBox="0 0 48 48"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.19802 38.113H28.1858C30.1028 38.113 31.6553 36.5582 31.6553 34.6435V6.32497C31.6553 4.40797 30.1005 2.85547 28.1858 2.85547H9.19802C7.28102 2.85547 5.72852 4.41022 5.72852 6.32497V34.6435C5.72627 36.5605 7.28102 38.113 9.19802 38.113Z"
        fill="#FFD05C"
      />
      <path
        d="M16.1279 41.8231H29.4524L39.0442 31.7634V11.5156C39.0442 9.36913 37.3049 7.62988 35.1584 7.62988H16.1279C13.9814 7.62988 12.2422 9.36913 12.2422 11.5156V37.9351C12.2399 40.0816 13.9814 41.8231 16.1279 41.8231Z"
        fill="#FFD05C"
      />
      <path
        d="M31.6552 34.5646V7.62988H16.1279C13.9814 7.62988 12.2422 9.36913 12.2422 11.5156V37.9351C12.2422 37.9959 12.2489 38.0544 12.2512 38.1129H28.1069C30.0667 38.1129 31.6552 36.5244 31.6552 34.5646Z"
        fill="#FCA235"
      />
      <path
        d="M31.3493 16.8822H18.4118C17.4173 16.8822 16.6118 16.0767 16.6118 15.0822C16.6118 14.0877 17.4173 13.2822 18.4118 13.2822H31.3493C32.3438 13.2822 33.1493 14.0877 33.1493 15.0822C33.1493 16.0767 32.3438 16.8822 31.3493 16.8822ZM31.3493 22.8425H18.4118C17.4173 22.8425 16.6118 22.037 16.6118 21.0425C16.6118 20.048 17.4173 19.2425 18.4118 19.2425H31.3493C32.3438 19.2425 33.1493 20.048 33.1493 21.0425C33.1493 22.037 32.3438 22.8425 31.3493 22.8425ZM25.6163 28.9737H18.4118C17.4173 28.9737 16.6118 28.1682 16.6118 27.1737C16.6118 26.1792 17.4173 25.3737 18.4118 25.3737H25.6186C26.6131 25.3737 27.4186 26.1792 27.4186 27.1737C27.4186 28.1682 26.6108 28.9737 25.6163 28.9737Z"
        fill="white"
      />
      <path
        d="M29.4525 41.8228L39.0443 31.7631V29.3398H31.3335C29.4345 29.3398 27.8955 30.8788 27.8955 32.7778V41.8206H29.4525V41.8228Z"
        fill="#FCA235"
      />
      <path
        d="M29.4525 41.8228L39.0443 31.7631V29.3398H31.3335C29.4345 29.3398 27.8955 30.8788 27.8955 32.7778V41.8206H29.4525V41.8228Z"
        fill="#FCA235"
      />
      <path
        d="M31.3335 29.3398C29.4345 29.3398 27.8955 30.8788 27.8955 32.7778V38.1126H28.107C30.0668 38.1126 31.6553 36.5241 31.6553 34.5643V29.3398H31.3335Z"
        fill="#F99026"
      />
    </svg>
  );
};

export default IconProfile;
