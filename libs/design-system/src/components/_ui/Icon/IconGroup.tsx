import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconGroup: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 32 32"
      fill="none"
      aria-label={alt}
    >
      <g id="icon / Add Group">
        <path
          id="Vector"
          d="M4.8 9.4402C4.8 7.78524 5.47428 6.19808 6.67452 5.02785C7.87475 3.85762 9.50261 3.2002 11.2 3.2002C12.8974 3.2002 14.5253 3.85762 15.7255 5.02785C16.9257 6.19808 17.6 7.78524 17.6 9.4402C17.6 11.0951 16.9257 12.6823 15.7255 13.8525C14.5253 15.0228 12.8974 15.6802 11.2 15.6802C9.50261 15.6802 7.87475 15.0228 6.67452 13.8525C5.47428 12.6823 4.8 11.0951 4.8 9.4402ZM0 26.7123C0 21.9104 3.99 18.0202 8.915 18.0202H13.485C18.41 18.0202 22.4 21.9104 22.4 26.7123C22.4 27.5118 21.735 28.1602 20.915 28.1602H1.485C0.665 28.1602 0 27.5118 0 26.7123ZM25.2 18.4102V15.2902H22C21.335 15.2902 20.8 14.7686 20.8 14.1202C20.8 13.4718 21.335 12.9502 22 12.9502H25.2V9.83019C25.2 9.18182 25.735 8.6602 26.4 8.6602C27.065 8.6602 27.6 9.18182 27.6 9.83019V12.9502H30.8C31.465 12.9502 32 13.4718 32 14.1202C32 14.7686 31.465 15.2902 30.8 15.2902H27.6V18.4102C27.6 19.0586 27.065 19.5802 26.4 19.5802C25.735 19.5802 25.2 19.0586 25.2 18.4102Z"
          fill="black"
        />
      </g>
    </svg>
  );
};

export default IconGroup;
