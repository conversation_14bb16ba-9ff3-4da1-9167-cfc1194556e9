import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconDownload: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 50 50"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_3167_5588)">
        <path
          d="M50 46.875C50 48.6009 48.6009 50 46.875 50H3.125C1.39912 50 0 48.6009 0 46.875C0 45.1491 1.39912 43.75 3.125 43.75H46.875C48.6009 43.75 50 45.1491 50 46.875ZM22.7903 36.9754C23.4006 37.5856 24.2002 37.8907 25 37.8907C25.7996 37.8907 26.5996 37.5855 27.2097 36.9754L38.2817 25.9034C39.5021 24.683 39.5021 22.7044 38.2817 21.484C37.0613 20.2636 35.0827 20.2636 33.8623 21.484L28.125 27.2213V3.125C28.125 1.39912 26.7259 0 25 0C23.2741 0 21.875 1.39912 21.875 3.125V27.2213L16.1377 21.484C14.9173 20.2636 12.9387 20.2636 11.7183 21.484C10.4979 22.7044 10.4979 24.683 11.7183 25.9034L22.7903 36.9754Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_3167_5588">
          <rect width="50" height="50" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconDownload;
