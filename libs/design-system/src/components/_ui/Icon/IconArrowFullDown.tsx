interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconArrowFullDown: React.FC<IconProps> = ({
  size,
  alt,
  className,
  color,
}) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 7 5"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.14738 4.70149C3.85965 5.0995 3.14035 5.0995 2.85262 4.70149L0.101265 0.895523C-0.186458 0.497513 0.173196 0 0.748643 0H6.25136C6.8268 0 7.18646 0.497513 6.89874 0.895523L4.14738 4.70149Z"
        fill={color}
      />
    </svg>
  );
};

export default IconArrowFullDown;
