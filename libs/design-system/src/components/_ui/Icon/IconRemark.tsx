import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconRemark: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 10 10"
      fill="none"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_1266_2163)">
        <path
          d="M7.88477 2.86621L7.60254 3.14844L7.71484 3.03613C7.80469 2.93555 7.80078 2.78125 7.7041 2.68457L7.88477 2.86621ZM6.35254 1.33105L6.63477 1.61328L6.52246 1.50098C6.42188 1.41113 6.26758 1.41504 6.1709 1.51172L6.35254 1.33105ZM9.37793 9.03906C9.37793 8.81836 9.19922 8.63867 8.97754 8.63867H0.977539C0.756836 8.63867 0.577148 8.81738 0.577148 9.03906C0.577148 9.25977 0.755859 9.43945 0.977539 9.43945H8.97852C9.19922 9.43848 9.37793 9.25977 9.37793 9.03906ZM1.60938 8.01367C1.83008 8.01367 2.00977 7.83496 2.00977 7.61328V5.51465C2.00977 5.29395 1.83105 5.11426 1.60938 5.11426C1.38867 5.11426 1.20898 5.29297 1.20898 5.51465V7.61328C1.20898 7.83496 1.38867 8.01367 1.60938 8.01367Z"
          fill="#242424"
        />
        <path
          d="M1.20898 7.61426C1.20898 7.83496 1.3877 8.01465 1.60938 8.01465H3.70215C3.92285 8.01465 4.10254 7.83594 4.10254 7.61426C4.10254 7.39355 3.92383 7.21387 3.70215 7.21387H1.60938C1.38867 7.21387 1.20898 7.39355 1.20898 7.61426Z"
          fill="#242424"
        />
        <path
          d="M8.54395 2.39793C8.54297 2.39695 8.54102 2.39598 8.54004 2.39402L6.78809 0.642071C6.52734 0.415509 6.13086 0.425274 5.88281 0.674298C5.88184 0.675274 5.88086 0.677228 5.87891 0.678204L1.32227 5.23387C1.16602 5.39012 1.16602 5.64305 1.32227 5.7993C1.47852 5.95555 1.73145 5.95555 1.8877 5.7993L6.35059 1.33641L7.88086 2.8657L3.41895 7.32762C3.2627 7.48387 3.2627 7.7368 3.41895 7.89305C3.5752 8.0493 3.82812 8.0493 3.98438 7.89305L8.58789 3.28953C8.80176 3.02879 8.78711 2.64207 8.54395 2.39793Z"
          fill="#242424"
        />
      </g>
      <defs>
        <clipPath id="clip0_1266_2163">
          <rect
            width="10"
            height="10"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconRemark;
