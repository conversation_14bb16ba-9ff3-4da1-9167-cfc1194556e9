import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconPlay: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 46 46"
      fill="none"
      aria-label={alt}
    >
      <g id="icon / Play">
        <path
          d="M25 3C13.4199 3 4 12.6451 4 24.5C4 36.3549 13.4199 46 25 46C36.5801 46 46 36.3549 46 24.5C46 12.6451 36.5801 3 25 3Z"
          fill="black"
        />
        <path
          d="M25 0C11.2142 0 0 11.2152 0 25C0 38.7848 11.2142 50 25 50C38.7858 50 50 38.7848 50 25C50 11.2152 38.7858 0 25 0ZM34.9386 25.8759L20.3553 35.2509C20.1873 35.3593 19.9916 35.4169 19.7917 35.4167C19.6208 35.4167 19.4479 35.3739 19.2933 35.2896C19.1288 35.2001 18.9915 35.0679 18.8959 34.907C18.8003 34.746 18.7499 34.5622 18.75 34.375V15.625C18.75 15.2436 18.9575 14.8936 19.2933 14.7104C19.6229 14.5294 20.0358 14.5405 20.3553 14.7491L34.9386 24.1241C35.2356 24.3153 35.4167 24.646 35.4167 25C35.4167 25.354 35.2356 25.6846 34.9386 25.8759Z"
          fill="white"
        />
      </g>
    </svg>
  );
};

export default IconPlay;
