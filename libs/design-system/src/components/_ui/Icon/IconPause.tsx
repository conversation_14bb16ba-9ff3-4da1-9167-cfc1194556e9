import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconPause: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 46 46"
      fill="none"
      aria-label={alt}
    >
      <path
        d="M25.5 8C15.8499 8 8 15.8506 8 25.5C8 35.1494 15.8499 43 25.5 43C35.1501 43 43 35.1494 43 25.5C43 15.8506 35.1501 8 25.5 8Z"
        fill="black"
      />
      <path
        d="M25 0C11.1875 0 0 11.1875 0 25C0 38.8125 11.1875 50 25 50C38.8125 50 50 38.8125 50 25C50 11.1875 38.8125 0 25 0ZM22.25 31.25C22.25 32.8125 21 34 19.5 34C17.9375 34 16.75 32.75 16.75 31.25V18.75C16.6875 17.25 17.9375 16 19.4375 16C21 16 22.25 17.25 22.25 18.75V31.25ZM33.3125 31.25C33.3125 32.8125 32.0625 34 30.5625 34C29 34 27.8125 32.75 27.8125 31.25V18.75C27.75 17.25 29 16 30.5 16C32.0625 16 33.3125 17.25 33.3125 18.75V31.25Z"
        fill="white"
      />
    </svg>
  );
};

export default IconPause;
