import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconDropdownArrow: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 11 9"
      aria-label={alt}
    >
      <g id="icon / Option dropdown">
        <path
          id="Option dropdown"
          d="M4.48269 8.02239C4.93483 8.6592 6.06517 8.6592 6.51731 8.02239L10.8409 1.93284C11.293 1.29602 10.7278 0.5 9.82356 0.5H1.17644C0.272165 0.5 -0.293007 1.29602 0.159131 1.93284L4.48269 8.02239Z"
          fill="currentColor"
        />
      </g>
    </svg>
  );
};

export default IconDropdownArrow;
