import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconConnecting: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 14 14"
      fill="none"
    >
      <path
        d="M5.22236 1.09762C4.49436 0.91282 3.73136 0.99122 3.13356 1.32582C2.52456 1.66602 2.09756 2.26942 2.07656 3.07652C2.04436 4.33792 2.33626 6.03052 3.49546 8.01712C4.63996 9.97922 5.90346 11.1552 6.95205 11.8538C7.61425 12.2955 8.34576 12.3074 8.97086 12.0197C9.58686 11.7362 10.0867 11.1699 10.3464 10.4678C10.4109 10.2932 10.4368 10.1066 10.4223 9.92101C10.4078 9.73539 10.3532 9.55512 10.2624 9.39262L9.59456 8.20122C9.38927 7.83454 9.0589 7.55397 8.66381 7.41079C8.26871 7.26761 7.83529 7.27138 7.44276 7.42142L6.97656 7.59992C6.75046 7.68602 6.54256 7.64612 6.42496 7.52992C6.02946 7.14142 5.74666 6.61432 5.61856 6.04592C5.57796 5.86672 5.66056 5.65532 5.85796 5.50622L6.27866 5.18842C6.61877 4.93177 6.85356 4.55986 6.93901 4.14244C7.02445 3.72502 6.95467 3.29076 6.74275 2.92112L6.06236 1.73462C5.97218 1.57756 5.85136 1.44024 5.70705 1.33081C5.56275 1.22138 5.39792 1.14208 5.22236 1.09762ZM2.77656 3.09542C2.79056 2.55362 3.06496 2.16652 3.47516 1.93692C3.89656 1.70102 4.47196 1.62892 5.05016 1.77592C5.13488 1.79732 5.21443 1.83555 5.28406 1.88835C5.35369 1.94114 5.41198 2.00742 5.45546 2.08322L6.13516 3.26972C6.26228 3.49141 6.3042 3.75186 6.25304 4.00225C6.20188 4.25263 6.06116 4.47576 5.85726 4.62982L5.43656 4.94762C5.07256 5.22202 4.82056 5.69242 4.93536 6.19992C5.09076 6.88732 5.43446 7.53902 5.93426 8.02972C6.29966 8.38812 6.82046 8.40912 7.22646 8.25372L7.69266 8.07522C7.9282 7.98506 8.18832 7.98269 8.42547 8.06855C8.66261 8.15441 8.86093 8.32276 8.98416 8.54282L9.65126 9.73492C9.73526 9.88472 9.74926 10.0639 9.68976 10.2249C9.48676 10.7744 9.10526 11.1867 8.67826 11.3834C8.26036 11.5759 7.78786 11.5703 7.34056 11.2714C6.38086 10.6316 5.19156 9.53472 4.09956 7.66432C3.00686 5.78832 2.74716 4.22452 2.77656 3.09542Z"
        fill="currentColor"
      />
      <path
        d="M11.882 1.525C11.9258 1.6125 11.9258 1.74375 11.9258 1.83125V4.01875C11.9258 4.28125 11.7508 4.45625 11.4883 4.45625C11.2258 4.45625 11.0508 4.28125 11.0508 4.01875V2.4875L9.21328 4.325C9.08203 4.45625 8.81953 4.45625 8.64453 4.28125C8.46953 4.10625 8.46953 3.84375 8.60078 3.7125L10.4383 1.875H8.86328C8.60078 1.875 8.42578 1.7 8.42578 1.4375C8.42578 1.175 8.60078 1 8.86328 1H11.0508C11.182 1 11.2695 1 11.357 1.04375C11.4883 1.04375 11.6195 1.0875 11.707 1.175C11.7945 1.2625 11.882 1.4375 11.882 1.525Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default IconConnecting;
