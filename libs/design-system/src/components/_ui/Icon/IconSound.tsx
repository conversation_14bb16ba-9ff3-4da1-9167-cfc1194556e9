import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconSound: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 18 18"
    >
      <g clipPath="url(#clip0_3381_3598)">
        <path d="M9.00542 14.6111C9.00546 14.7663 8.96129 14.9183 8.87806 15.0493C8.79483 15.1802 8.676 15.2848 8.53548 15.3507C8.39506 15.4168 8.23873 15.4415 8.08478 15.4219C7.93084 15.4023 7.78565 15.3393 7.66622 15.2402L3.19747 11.5372H0.817408C0.710066 11.5373 0.603767 11.5162 0.504584 11.4751C0.405401 11.4341 0.315279 11.3739 0.239368 11.298C0.163457 11.2221 0.103245 11.132 0.0621735 11.0328C0.0211015 10.9337 -2.54074e-05 10.8274 2.29304e-08 10.72V7.46335C1.68605e-08 7.24654 0.0861134 7.03861 0.239402 6.88528C0.39269 6.73196 0.6006 6.6458 0.817408 6.64574H3.19766L7.66642 2.94274C7.78581 2.84357 7.93103 2.78053 8.085 2.76103C8.23898 2.74153 8.39533 2.76638 8.53567 2.83266C8.67615 2.8986 8.79494 3.00318 8.87816 3.13416C8.96138 3.26514 9.00559 3.41711 9.00561 3.57229L9.00542 14.6111ZM12.1468 13.3338C12.0298 13.3423 11.9124 13.3255 11.8026 13.2846C11.6927 13.2436 11.5929 13.1795 11.5101 13.0966L11.4008 12.9869C11.262 12.8484 11.1777 12.6646 11.1632 12.4692C11.1487 12.2737 11.205 12.0795 11.3218 11.922C11.9313 11.1046 12.2594 10.1116 12.2571 9.09197C12.2571 7.98803 11.8902 6.95259 11.1961 6.09745C11.0685 5.94059 11.0037 5.74199 11.0141 5.54008C11.0245 5.33816 11.1094 5.14728 11.2524 5.00435L11.3615 4.89504C11.5248 4.73175 11.7436 4.64276 11.9804 4.65688C12.0945 4.6626 12.2061 4.69216 12.3081 4.74364C12.4101 4.79512 12.5002 4.86739 12.5726 4.95579C13.5355 6.13402 14.0441 7.56453 14.0441 9.09216C14.0441 10.5149 13.5943 11.8692 12.7431 13.008C12.6728 13.1018 12.5832 13.1795 12.4803 13.2357C12.3775 13.2919 12.2637 13.3254 12.1468 13.3338ZM15.5263 15.8599C15.4532 15.9463 15.363 16.0165 15.2614 16.0663C15.1598 16.116 15.049 16.1442 14.936 16.1489C14.823 16.1537 14.7102 16.135 14.6048 16.094C14.4994 16.0529 14.4036 15.9905 14.3235 15.9106L14.2161 15.8032C14.0715 15.6585 13.9863 15.4648 13.9775 15.2604C13.9687 15.056 14.0369 14.8557 14.1685 14.6991C15.488 13.1285 16.2119 11.1432 16.2131 9.09197C16.214 6.96426 15.4353 4.90996 14.0242 3.31749C13.8864 3.16179 13.8131 2.95942 13.8192 2.75156C13.8253 2.54371 13.9104 2.346 14.0571 2.19866L14.1643 2.09128C14.2422 2.01115 14.3362 1.94833 14.44 1.90693C14.5438 1.86553 14.6552 1.84647 14.7669 1.85099C14.8784 1.85422 14.9879 1.88021 15.089 1.92736C15.19 1.9745 15.2803 2.04181 15.3543 2.12514C17.0599 4.04488 18.0014 6.52399 18 9.09197C17.9994 11.5691 17.1232 13.9662 15.5263 15.8599Z" fill="#636363" />
      </g>
      <defs>
        <clipPath id="clip0_3381_3598">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconSound;
