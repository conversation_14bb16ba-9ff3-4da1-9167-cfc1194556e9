interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconTriangleDown: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label={alt}
    >
      <path
        d="M4 6H11L7.5 10.5L4 6Z"
        fill="currentColor"
      ></path>
    </svg>
  );
};

export default IconTriangleDown;
