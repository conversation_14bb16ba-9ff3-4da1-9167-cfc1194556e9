import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconFullLogo: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 126 41"
      aria-label={alt}
    >
      <path
        d="M66.4971 32.3604C65.6097 33.5496 64.2131 34.1405 62.3074 34.1405C60.4017 34.1405 58.976 33.5063 58.0377 32.245C57.0921 30.9838 56.6266 29.1676 56.6266 26.7892V15.6541C56.6266 11.8486 57.6304 9.47027 59.6452 8.52613C60.3871 8.18018 61.2891 8 62.3438 8C65.9661 8 67.7845 10.2631 67.7845 14.7964V18.3135H63.9876V15.0486C63.9876 13.7297 63.8785 12.7928 63.653 12.2234C63.4276 11.6541 62.9911 11.373 62.3365 11.373C61.6819 11.373 61.2309 11.6757 60.9763 12.2739C60.7217 12.8721 60.5981 13.7946 60.5981 15.0342V26.9694C60.5981 28.3459 60.7217 29.3261 60.9618 29.9171C61.2091 30.5081 61.6528 30.8036 62.2929 30.8036C62.9402 30.8036 63.3839 30.5009 63.624 29.9027C63.8713 29.3045 63.9876 28.3099 63.9876 26.9261V23.2649H67.8427V26.8829C67.8355 29.3477 67.3845 31.1784 66.4971 32.3604Z"
        fill="#30302F"
      />
      <path
        d="M74.0109 33.564V8H79.8008C81.6992 8 83.074 8.55495 83.925 9.66486C84.7833 10.7748 85.2052 12.4108 85.2052 14.5658L85.2197 26.0973C85.2197 28.6703 84.7906 30.5586 83.9323 31.7622C83.074 32.9658 81.6483 33.564 79.6699 33.564H74.0109ZM77.9387 30.5441H79.2262C79.9463 30.5441 80.4772 30.3135 80.8046 29.845C81.1319 29.3838 81.2992 28.5261 81.2992 27.2865L81.2846 14.6667C81.2846 13.3405 81.1173 12.3964 80.79 11.8342C80.4627 11.2721 79.8953 10.991 79.0952 10.991H77.9387V30.5441Z"
        fill="#30302F"
      />
      <path
        d="M91.2497 26.6811V24.4036H94.9739V26.9766C94.9739 28.4036 95.0975 29.3982 95.3376 29.9748C95.5849 30.5514 96.0431 30.8396 96.7123 30.8396C97.3888 30.8396 97.8325 30.6018 98.0507 30.1261C98.2689 29.6504 98.378 28.8793 98.378 27.8054C98.378 26.7315 98.1816 25.7946 97.7888 24.9946C97.396 24.1946 96.7778 23.3586 95.9267 22.4865L94.021 20.5405C93.1336 19.618 92.4426 18.6667 91.948 17.6793C91.4534 16.6919 91.2061 15.5459 91.2061 14.2486C91.2061 12.1369 91.6134 10.573 92.4353 9.54234C93.2573 8.51892 94.6684 8 96.6687 8C98.6689 8 100.029 8.5982 100.735 9.79459C101.44 10.991 101.797 12.7351 101.797 15.0342V16.6198H98.1743V14.7892C98.1743 13.5495 98.0652 12.6631 97.8543 12.1225C97.6361 11.582 97.2287 11.3153 96.6177 11.3153C96.0067 11.3153 95.5703 11.5171 95.3012 11.9279C95.0321 12.3315 94.9011 12.9441 94.9011 13.7586C94.9011 14.573 95.0393 15.2216 95.3085 15.7045C95.5849 16.1874 96.0722 16.8072 96.7923 17.564L98.9162 19.8414C99.9782 20.9874 100.786 22.1405 101.324 23.3153C101.869 24.4901 102.139 25.7658 102.139 27.1495C102.139 29.5423 101.731 31.3081 100.924 32.4541C100.109 33.6 98.6908 34.1694 96.6687 34.1694C94.6393 34.1694 93.2282 33.5351 92.4353 32.2595C91.6425 30.9838 91.2497 29.1243 91.2497 26.6811Z"
        fill="#30302F"
      />
      <path
        d="M107.514 26.6811V24.4036H111.238V26.9766C111.238 28.4036 111.362 29.3982 111.602 29.9748C111.849 30.5514 112.307 30.8396 112.976 30.8396C113.653 30.8396 114.097 30.6018 114.315 30.1261C114.533 29.6504 114.642 28.8793 114.642 27.8054C114.642 26.7315 114.446 25.7946 114.053 24.9946C113.66 24.1946 113.042 23.3586 112.191 22.4865L110.285 20.5405C109.398 19.618 108.707 18.6667 108.212 17.6793C107.718 16.6919 107.47 15.5459 107.47 14.2486C107.47 12.1369 107.878 10.573 108.699 9.54234C109.521 8.51892 110.933 8 112.933 8C114.933 8 116.293 8.5982 116.999 9.79459C117.704 10.991 118.061 12.7351 118.061 15.0342V16.6198H114.438V14.7892C114.438 13.5495 114.329 12.6631 114.118 12.1225C113.9 11.582 113.493 11.3153 112.882 11.3153C112.271 11.3153 111.834 11.5171 111.565 11.9279C111.296 12.3315 111.165 12.9441 111.165 13.7586C111.165 14.573 111.303 15.2216 111.573 15.7045C111.849 16.1874 112.336 16.8072 113.056 17.564L115.18 19.8414C116.242 20.9874 117.05 22.1405 117.588 23.3153C118.134 24.4901 118.403 25.7658 118.403 27.1495C118.403 29.5423 117.995 31.3081 117.188 32.4541C116.373 33.6 114.955 34.1694 112.933 34.1694C110.903 34.1694 109.492 33.5351 108.699 32.2595C107.914 30.9838 107.514 29.1243 107.514 26.6811Z"
        fill="#30302F"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M121.69 1.31169C120.064 1.31169 118.746 2.61813 118.746 4.2288C118.746 5.83948 120.064 7.14592 121.69 7.14592C123.317 7.14592 124.635 5.83948 124.635 4.2288C124.635 2.61813 123.317 1.31169 121.69 1.31169ZM118.074 4.2288C118.074 2.24913 119.694 0.64502 121.69 0.64502C123.687 0.64502 125.307 2.24913 125.307 4.2288C125.307 6.20848 123.687 7.81259 121.69 7.81259C119.694 7.81259 118.074 6.20848 118.074 4.2288Z"
        fill="#30302F"
      />
      <path
        d="M121.487 3.93319C121.69 3.93319 121.843 3.89715 121.93 3.81787C122.018 3.73859 122.069 3.61607 122.069 3.4431C122.069 3.27012 122.025 3.1476 121.93 3.07553C121.843 3.00346 121.698 2.96742 121.487 2.96742H121.072V3.94039H121.487M121.072 4.60346V6.03769H120.09V2.25391H121.596C122.098 2.25391 122.469 2.34039 122.701 2.50616C122.934 2.67192 123.051 2.93859 123.051 3.29895C123.051 3.5512 122.992 3.75301 122.869 3.91156C122.745 4.07012 122.563 4.19265 122.316 4.26472C122.454 4.29355 122.571 4.36562 122.68 4.47373C122.789 4.58183 122.898 4.74039 123.007 4.95661L123.538 6.03048H122.491L122.025 5.09355C121.93 4.90616 121.836 4.77643 121.741 4.70436C121.647 4.63228 121.516 4.60346 121.356 4.60346H121.072Z"
        fill="#30302F"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M35.1584 6.75396C37.5222 9.09257 37.5222 12.8842 35.1584 15.2228C32.7946 17.5614 28.9622 17.5614 26.5984 15.2228C24.2346 12.8842 24.2346 9.09257 26.5984 6.75396C28.9622 4.41535 32.7946 4.41535 35.1584 6.75396ZM22 37.0002C31.5883 36.3643 39.2601 28.8331 39.9981 19.3764V19.3705H27.6363C24.5235 19.3705 22 21.8672 22 24.9468V37.0002Z"
        fill="#30302F"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.2478 6.74955C15.6116 9.08163 15.6116 12.8627 13.2478 15.1948C10.884 17.5268 7.05153 17.5268 4.68774 15.1948C2.32395 12.8627 2.32395 9.08163 4.68774 6.74955C7.05153 4.41747 10.884 4.41747 13.2478 6.74955ZM17.9981 37.0004C8.40982 36.3662 0.738017 28.856 0 19.4258H12.3618C15.4745 19.4258 17.9981 21.9154 17.9981 24.9864V37.0004Z"
        fill="#F7971D"
      />
    </svg>
  );
};

export default IconFullLogo;
