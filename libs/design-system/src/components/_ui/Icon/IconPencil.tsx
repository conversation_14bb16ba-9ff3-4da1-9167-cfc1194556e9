import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconPencil: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 20 20"
    >
      <g
        id="icon / Edit"
        clipPath="url(#clip0_510_804)"
      >
        <path
          id="Vector"
          d="M12.3511 3.34267L1.3468 14.3593C1.29139 14.4149 1.2519 14.4844 1.23245 14.5605L0.0126888 19.4614C-0.00525069 19.5341 -0.0041513 19.6102 0.0158808 19.6824C0.035913 19.7546 0.0742009 19.8204 0.127046 19.8734C0.208216 19.9544 0.318117 19.9999 0.432716 20C0.468069 20 0.503286 19.9956 0.537571 19.987L5.43329 18.7658C5.50941 18.7466 5.5789 18.7071 5.63431 18.6515L16.6396 7.6356L12.3511 3.34267ZM19.3658 1.84056L18.1408 0.61436C17.3221 -0.205193 15.8952 -0.20438 15.0775 0.61436L13.577 2.11647L17.8653 6.40924L19.3658 4.90718C19.7747 4.49797 20 3.95327 20 3.37397C20 2.79467 19.7747 2.24997 19.3658 1.84056Z"
          // fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_510_804">
          <rect
            width="20"
            height="20"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconPencil;
