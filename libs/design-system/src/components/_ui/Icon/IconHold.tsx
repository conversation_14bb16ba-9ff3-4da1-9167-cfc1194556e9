import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconHold: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 32 32"
      aria-label={alt}
    >
      <path
        d="M6.14392 0C3.81192 0 1.91992 1.79167 1.91992 4V28C1.91992 30.2083 3.81192 32 6.14392 32H8.95992C11.2919 32 13.1839 30.2083 13.1839 28V4C13.1839 1.79167 11.2919 0 8.95992 0H6.14392ZM23.0399 0C20.7079 0 18.8159 1.79167 18.8159 4V28C18.8159 30.2083 20.7079 32 23.0399 32H25.8559C28.1879 32 30.0799 30.2083 30.0799 28V4C30.0799 1.79167 28.1879 0 25.8559 0H23.0399Z"
        // fill="black"
      />
    </svg>
  );
};

export default IconHold;
