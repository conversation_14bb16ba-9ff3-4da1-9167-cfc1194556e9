import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconAddressBook: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 20 20"
    >
      <g
        id="icon / Address book"
        clipPath="url(#clip0_510_275)"
      >
        <path
          id="Vector"
          d="M4.76918 10C4.76918 10.4249 4.42489 10.7692 3.99995 10.7692H2.46149C2.03656 10.7692 1.69226 10.4249 1.69226 10C1.69226 9.57507 2.03656 9.23077 2.46149 9.23077H3.99995C4.42493 9.23077 4.76918 9.57507 4.76918 10ZM4.76918 5.07692C4.76918 4.65199 4.42489 4.30769 3.99995 4.30769H2.46149C2.03656 4.30769 1.69226 4.65199 1.69226 5.07692C1.69226 5.50186 2.03656 5.84615 2.46149 5.84615H3.99995C4.42493 5.84615 4.76918 5.50186 4.76918 5.07692ZM4.76918 14.9231C4.76918 14.4981 4.42489 14.1539 3.99995 14.1539H2.46149C2.03656 14.1539 1.69226 14.4981 1.69226 14.9231C1.69226 15.3481 2.03656 15.6924 2.46149 15.6924H3.99995C4.42493 15.6924 4.76918 15.348 4.76918 14.9231ZM18.3076 2.46154V17.5385C18.3076 18.8979 17.2055 20 15.8462 20H5.07697C3.7176 20 2.61543 18.8979 2.61543 17.5385V16.3077H4.00004C4.76344 16.3077 5.38466 15.6864 5.38466 14.9231C5.38466 14.1597 4.76344 13.5384 4.00004 13.5384H2.61534V11.3845H3.99995C4.76335 11.3845 5.38457 10.7633 5.38457 9.99991C5.38457 9.23652 4.76335 8.61529 3.99995 8.61529H2.61534V6.46145H3.99995C4.76335 6.46145 5.38457 5.84023 5.38457 5.07683C5.38457 4.31344 4.76335 3.69222 3.99995 3.69222H2.61534V2.46154C2.61534 1.10217 3.71751 0 5.07688 0H15.8461C17.2055 0 18.3076 1.10213 18.3076 2.46154ZM8.5495 7.29661C8.5495 8.60738 9.61226 9.67014 10.923 9.67014C12.2338 9.67014 13.2966 8.60738 13.2966 7.29661C13.2966 5.98584 12.2338 4.92303 10.923 4.92303C9.61231 4.92308 8.5495 5.98584 8.5495 7.29661ZM14.6153 13.0987C14.6153 11.6424 13.4344 10.4615 11.9781 10.4615H9.86828C8.41167 10.4615 7.23072 11.6425 7.23072 13.0987V14.1538H14.6153V13.0987Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_510_275">
          <rect
            width="20"
            height="20"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconAddressBook;
