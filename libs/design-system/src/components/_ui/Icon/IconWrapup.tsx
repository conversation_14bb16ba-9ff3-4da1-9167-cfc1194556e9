import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconWrapup: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 14 14"
      fill="none"
    >
      <path
        d="M10.708 9.20495C10.578 9.20532 10.4535 9.25701 10.3614 9.34879C10.2694 9.44056 10.2173 9.56498 10.2166 9.69495V10.9199H3.0836V3.07995H10.2166V5.67695C10.2164 5.74147 10.229 5.80538 10.2537 5.86501C10.2783 5.92463 10.3145 5.97878 10.3602 6.02434C10.4059 6.0699 10.4601 6.10595 10.5198 6.13043C10.5795 6.15491 10.6435 6.16732 10.708 6.16695C10.8381 6.16677 10.9628 6.11515 11.055 6.02336C11.1472 5.93156 11.1994 5.80704 11.2001 5.67695V3.03095C11.1956 2.84156 11.1347 2.65781 11.0252 2.5032C10.9157 2.34859 10.7626 2.23013 10.5855 2.16295C10.4678 2.11801 10.3425 2.09661 10.2166 2.09995H3.0836C2.89431 2.09537 2.70788 2.14672 2.54763 2.24757C2.38738 2.34843 2.26044 2.4943 2.1827 2.66695C2.13142 2.78164 2.10334 2.90535 2.1001 3.03095V10.9689C2.1071 11.2216 2.2149 11.4617 2.399 11.6367C2.5831 11.811 2.8295 11.9055 3.0836 11.8999H10.2166C10.4707 11.9055 10.7171 11.811 10.9012 11.6367C11.0853 11.4617 11.1931 11.2216 11.2001 10.9689V9.69495C11.2003 9.63037 11.1877 9.5664 11.163 9.50673C11.1383 9.44706 11.102 9.39287 11.0563 9.3473C11.0105 9.30174 10.9562 9.2657 10.8964 9.24126C10.8366 9.21683 10.7726 9.20449 10.708 9.20495Z"
        fill="currentColor"
      />
      <path
        d="M9.0463 5.37079C9.04614 5.30867 9.03348 5.24721 9.00907 5.19009C8.98466 5.13296 8.94899 5.08134 8.90419 5.03829C8.8123 4.94966 8.68956 4.90022 8.56189 4.90039H4.6846C4.5565 4.90039 4.43259 4.94939 4.34159 5.03829C4.29693 5.08139 4.26139 5.13305 4.2371 5.19017C4.21281 5.24729 4.20026 5.30872 4.2002 5.37079C4.2002 5.49539 4.2513 5.61439 4.3423 5.70259C4.4333 5.79079 4.5565 5.84049 4.6853 5.84049H8.56189C8.68971 5.84034 8.81255 5.79095 8.90489 5.70259C8.94919 5.65935 8.9845 5.60777 9.00877 5.55083C9.03303 5.49388 9.04579 5.43269 9.0463 5.37079ZM4.6853 7.25099C4.5565 7.25099 4.4333 7.29999 4.3423 7.38819C4.29771 7.43133 4.26226 7.483 4.23805 7.54012C4.21384 7.59724 4.20136 7.65865 4.20136 7.72069C4.20136 7.78273 4.21384 7.84414 4.23805 7.90126C4.26226 7.95838 4.29771 8.01005 4.3423 8.05319C4.4333 8.14139 4.5565 8.19109 4.6853 8.19109H7.1073C7.2361 8.19109 7.35929 8.14139 7.45029 8.05319C7.49509 8.01015 7.53076 7.95852 7.55517 7.9014C7.57958 7.84427 7.59224 7.78281 7.59239 7.72069C7.59224 7.65857 7.57958 7.59711 7.55517 7.53999C7.53076 7.48286 7.49509 7.43124 7.45029 7.38819C7.35812 7.29965 7.23511 7.25045 7.1073 7.25099H4.6853ZM11.7581 7.02699C11.6663 6.93787 11.543 6.88857 11.4151 6.88979C11.2873 6.88994 11.1644 6.93933 11.0721 7.02769L8.67319 9.36569L7.99 8.70069C7.89806 8.61157 7.77504 8.56173 7.64699 8.56173C7.51895 8.56173 7.39593 8.61157 7.304 8.70069C7.25923 8.74376 7.22359 8.79539 7.19918 8.85251C7.17476 8.90963 7.16209 8.97107 7.1619 9.03319C7.1619 9.15779 7.213 9.27819 7.304 9.36569L8.33159 10.3625C8.42333 10.4518 8.54657 10.5013 8.67459 10.5004C8.80239 10.5002 8.92521 10.4508 9.0176 10.3625L11.7581 7.69269C11.8026 7.64945 11.8381 7.59779 11.8625 7.54071C11.8869 7.48363 11.8997 7.42227 11.9002 7.36019C11.9004 7.29802 11.8879 7.23646 11.8634 7.17929C11.839 7.12212 11.8031 7.07054 11.7581 7.02769V7.02699Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default IconWrapup;
