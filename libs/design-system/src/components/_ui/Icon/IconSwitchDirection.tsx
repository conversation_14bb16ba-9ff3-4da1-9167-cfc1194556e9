
interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
    onClick?: () => void;
  }
  
  const IconSwitchDirection: React.FC<IconProps> = ({
    size,
    alt,
    className,
    onClick,
  }) => {
    return (
        <svg className={`${className}`}
        style={{ width: size, height: size }}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        aria-label={alt}
        onClick={onClick}>
        <rect x="0.5" y="0.5" width="19" height="19" rx="1.5" stroke="#E0E0E0"/>
        <path d="M14.7851 11.0039H8.33297C7.75397 11.0039 7.42871 11.329 7.42871 11.9125V14.2668C7.42871 14.8382 7.75684 15.1637 8.33105 15.1637H14.7951C15.3557 15.1637 15.686 14.8323 15.6875 14.2683V11.9004C15.6875 11.3292 15.3594 11.0039 14.7851 11.0039Z" fill="#636363"/>
        <path d="M9.33252 5.69628C9.33252 5.53932 9.30846 5.40124 9.26197 5.28297C9.17625 5.06846 9.01451 4.91835 8.78906 4.84452C8.78236 4.84233 8.77539 4.84001 8.7691 4.83809H8.76828C8.66615 4.80788 5.23574 4.81963 5.23574 4.81963C4.66439 4.81963 4.33887 5.14776 4.33887 5.72198V14.2828C4.33887 14.7886 4.60807 15.1059 5.07387 15.165L5.08344 15.1662L5.09834 15.1677C5.14455 15.1725 6.15996 15.1751 6.15996 15.1751L6.19086 11.1604C6.19082 10.7846 6.34004 10.4243 6.60569 10.1585C6.87134 9.89282 7.23166 9.7435 7.6074 9.74342H9.36342L9.33252 5.69628ZM11.6768 4.86831C12.3026 5.00708 12.8958 5.32891 13.3922 5.79881C13.7119 6.10024 13.977 6.45469 14.1758 6.84649L14.8699 6.34446L15.1375 6.15141C15.1863 6.11617 15.2441 6.09543 15.3042 6.09157C15.3643 6.08771 15.4243 6.10089 15.4772 6.1296C15.5302 6.1583 15.574 6.20136 15.6035 6.25383C15.6331 6.3063 15.6473 6.36605 15.6444 6.42622L15.501 9.42036C15.4987 9.46827 15.4857 9.51506 15.4629 9.55727C15.4401 9.59947 15.4081 9.63601 15.3692 9.66418C15.3304 9.69235 15.2858 9.71143 15.2386 9.72001C15.1914 9.72859 15.1428 9.72645 15.0966 9.71376L12.1995 8.91819C12.1414 8.90224 12.089 8.8702 12.0483 8.82578C12.0076 8.78135 11.9802 8.72634 11.9694 8.66706C11.9586 8.60779 11.9647 8.54666 11.9871 8.49071C12.0095 8.43477 12.0472 8.38627 12.0959 8.35081L13.0447 7.66448C12.9258 7.34893 12.7221 7.04665 12.4524 6.79167C12.1402 6.49608 11.7596 6.2869 11.3807 6.20296C11.2922 6.18433 11.2084 6.14835 11.1339 6.0971C11.0594 6.04584 10.9959 5.98032 10.9469 5.90434C10.898 5.82836 10.8645 5.74342 10.8486 5.65444C10.8327 5.56545 10.8346 5.4742 10.8542 5.38595C10.8737 5.2977 10.9106 5.21421 10.9627 5.14031C11.0148 5.06641 11.081 5.00358 11.1575 4.95543C11.234 4.90729 11.3193 4.8748 11.4084 4.85985C11.4976 4.84489 11.5888 4.84777 11.6768 4.86831Z" fill="#636363"/>
        </svg>
    );
  };
  
  export default IconSwitchDirection;
  