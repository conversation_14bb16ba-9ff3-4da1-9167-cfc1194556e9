import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconFilter: React.FC<IconProps> = ({ size, alt }) => {
  return (
    <svg
      style={{ width: size, height: size }}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 37 37"
      aria-label={alt}
    >
      <path
        d="M0 4C0 1.79086 1.79086 0 4 0H33C35.2091 0 37 1.79086 37 4V33C37 35.2091 35.2091 37 33 37H4C1.79086 37 0 35.2091 0 33V4Z"
        fill="#00A3FF"
      />
      <path
        d="M0.5 4C0.5 2.067 2.067 0.5 4 0.5H33C34.933 0.5 36.5 2.067 36.5 4V33C36.5 34.933 34.933 36.5 33 36.5H4C2.067 36.5 0.5 34.933 0.5 33V4Z"
        fill="white"
      />
      <path
        d="M0.5 4C0.5 2.067 2.067 0.5 4 0.5H33C34.933 0.5 36.5 2.067 36.5 4V33C36.5 34.933 34.933 36.5 33 36.5H4C2.067 36.5 0.5 34.933 0.5 33V4Z"
        stroke="black"
      />
      <g clipPath="url(#clip0_2089_6802)">
        <path
          d="M28.998 25.8493C28.998 26.1278 28.8874 26.3948 28.6905 26.5917C28.4936 26.7886 28.2266 26.8992 27.9481 26.8992H18.3058C18.092 27.5129 17.6925 28.0449 17.1628 28.4213C16.633 28.7978 15.9992 29 15.3493 29C14.6994 29 14.0656 28.7978 13.5359 28.4213C13.0061 28.0449 12.6066 27.5129 12.3928 26.8992H9.0499C8.77145 26.8992 8.5044 26.7886 8.30751 26.5917C8.11061 26.3948 8 26.1278 8 25.8493C8 25.5709 8.11061 25.3038 8.30751 25.1069C8.5044 24.91 8.77145 24.7994 9.0499 24.7994H12.3928C12.6066 24.1857 13.0061 23.6537 13.5359 23.2773C14.0656 22.9009 14.6994 22.6986 15.3493 22.6986C15.9992 22.6986 16.633 22.9009 17.1628 23.2773C17.6925 23.6537 18.092 24.1857 18.3058 24.7994H27.9481C28.2266 24.7994 28.4936 24.91 28.6905 25.1069C28.8874 25.3038 28.998 25.5709 28.998 25.8493ZM27.9481 17.4501H25.6551C25.4413 16.8364 25.0418 16.3044 24.5121 15.928C23.9823 15.5515 23.3485 15.3493 22.6986 15.3493C22.0487 15.3493 21.4149 15.5515 20.8852 15.928C20.3554 16.3044 19.9559 16.8364 19.7421 17.4501H9.0499C8.77145 17.4501 8.5044 17.5607 8.30751 17.7576C8.11061 17.9545 8 18.2215 8 18.5C8 18.7785 8.11061 19.0455 8.30751 19.2424C8.5044 19.4393 8.77145 19.5499 9.0499 19.5499H19.7421C19.9559 20.1636 20.3554 20.6956 20.8852 21.072C21.4149 21.4485 22.0487 21.6507 22.6986 21.6507C23.3485 21.6507 23.9823 21.4485 24.5121 21.072C25.0418 20.6956 25.4413 20.1636 25.6551 19.5499H27.9481C28.2266 19.5499 28.4936 19.4393 28.6905 19.2424C28.8874 19.0455 28.998 18.7785 28.998 18.5C28.998 18.2215 28.8874 17.9545 28.6905 17.7576C28.4936 17.5607 28.2266 17.4501 27.9481 17.4501ZM9.0499 12.2006H14.4926C14.7064 12.8143 15.1059 13.3463 15.6357 13.7227C16.1654 14.0991 16.7992 14.3014 17.4491 14.3014C18.099 14.3014 18.7328 14.0991 19.2626 13.7227C19.7923 13.3463 20.1918 12.8143 20.4056 12.2006H27.9481C28.2266 12.2006 28.4936 12.09 28.6905 11.8931C28.8874 11.6962 28.998 11.4291 28.998 11.1507C28.998 10.8722 28.8874 10.6052 28.6905 10.4083C28.4936 10.2114 28.2266 10.1008 27.9481 10.1008H20.4056C20.1918 9.48708 19.7923 8.95511 19.2626 8.57868C18.7328 8.20224 18.099 8 17.4491 8C16.7992 8 16.1654 8.20224 15.6357 8.57868C15.1059 8.95511 14.7064 9.48708 14.4926 10.1008H9.0499C8.77145 10.1008 8.5044 10.2114 8.30751 10.4083C8.11061 10.6052 8 10.8722 8 11.1507C8 11.4291 8.11061 11.6962 8.30751 11.8931C8.5044 12.09 8.77145 12.2006 9.0499 12.2006Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_2089_6802">
          <rect
            width="21"
            height="21"
            fill="white"
            transform="translate(8 8)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconFilter;
