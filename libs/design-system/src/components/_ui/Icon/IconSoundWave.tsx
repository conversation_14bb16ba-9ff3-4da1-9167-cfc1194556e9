import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconSoundWave: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 18 18"
    >
      <g clipPath="url(#clip0_3381_3257)">
        <path fillRule="evenodd" clipRule="evenodd" d="M0 3.33984C0 3.63108 0.236109 3.86719 0.527344 3.86719C0.818578 3.86719 1.05469 3.63108 1.05469 3.33984V1.05469H3.33984C3.63108 1.05469 3.86719 0.818578 3.86719 0.527344C3.86719 0.236109 3.63108 0 3.33984 0H0.527344C0.236109 0 0 0.236109 0 0.527344V3.33984ZM0.527344 18H3.33984C3.63108 18 3.86719 17.7639 3.86719 17.4727C3.86719 17.1814 3.63108 16.9453 3.33984 16.9453H1.05469V14.6602C1.05469 14.3689 0.818578 14.1328 0.527344 14.1328C0.236109 14.1328 0 14.3689 0 14.6602V17.4727C0 17.7639 0.236109 18 0.527344 18ZM16.9453 3.33984C16.9453 3.63108 17.1814 3.86719 17.4727 3.86719C17.7639 3.86719 18 3.63108 18 3.33984V0.527344C18 0.236109 17.7639 0 17.4727 0H14.6602C14.3689 0 14.1328 0.236109 14.1328 0.527344C14.1328 0.818578 14.3689 1.05469 14.6602 1.05469H16.9453V3.33984ZM14.6602 18H17.4727C17.7639 18 18 17.7639 18 17.4727V14.6602C18 14.3689 17.7639 14.1328 17.4727 14.1328C17.1814 14.1328 16.9453 14.3689 16.9453 14.6602V16.9453H14.6602C14.3689 16.9453 14.1328 17.1814 14.1328 17.4727C14.1328 17.7639 14.3689 18 14.6602 18ZM8.64141 3.23859C8.64141 3.04055 8.80196 2.88 9 2.88C9.19804 2.88 9.35859 3.04055 9.35859 3.23859V14.7614C9.35859 14.9594 9.19804 15.12 9 15.12C8.80196 15.12 8.64141 14.9594 8.64141 14.7614V3.23859ZM10.0758 5.08157C10.0758 4.88353 10.2363 4.72298 10.4344 4.72298C10.6324 4.72298 10.793 4.88353 10.793 5.08157V12.9184C10.793 13.1164 10.6324 13.277 10.4344 13.277C10.2363 13.277 10.0758 13.1164 10.0758 12.9184V5.08157ZM11.5102 6.7154C11.5102 6.51736 11.6707 6.35681 11.8688 6.35681C12.0668 6.35681 12.2273 6.51734 12.2273 6.7154V11.2846C12.2273 11.4827 12.0668 11.6432 11.8688 11.6432C11.6707 11.6432 11.5102 11.4827 11.5102 11.2846V6.7154ZM12.9445 7.59493C12.9445 7.39689 13.1051 7.23634 13.3031 7.23634C13.5012 7.23634 13.6617 7.39689 13.6617 7.59493V10.4051C13.6617 10.6031 13.5012 10.7637 13.3031 10.7637C13.1051 10.7637 12.9445 10.6031 12.9445 10.4051V7.59493ZM14.3789 8.31212C14.3789 8.11408 14.5395 7.95353 14.7375 7.95353C14.9355 7.95353 15.0961 8.11408 15.0961 8.31212V9.68788C15.0961 9.88592 14.9355 10.0465 14.7375 10.0465C14.5395 10.0465 14.3789 9.88592 14.3789 9.68788V8.31212ZM7.20703 5.08157C7.20703 4.88353 7.36759 4.72298 7.56563 4.72298C7.76366 4.72298 7.92422 4.88353 7.92422 5.08157V12.9184C7.92422 13.1164 7.76366 13.277 7.56563 13.277C7.36759 13.277 7.20703 13.1164 7.20703 12.9184V5.08157ZM5.77266 6.7154C5.77266 6.51736 5.93321 6.35681 6.13125 6.35681C6.32929 6.35681 6.48984 6.51734 6.48984 6.7154V11.2846C6.48984 11.4827 6.32929 11.6432 6.13125 11.6432C5.93321 11.6432 5.77266 11.4827 5.77266 11.2846V6.7154ZM4.33828 7.59493C4.33828 7.39689 4.49884 7.23634 4.69688 7.23634C4.89491 7.23634 5.05547 7.39689 5.05547 7.59493V10.4051C5.05547 10.6031 4.89491 10.7637 4.69688 10.7637C4.49884 10.7637 4.33828 10.6031 4.33828 10.4051V7.59493ZM2.90391 8.31212C2.90391 8.11408 3.06446 7.95353 3.2625 7.95353C3.46054 7.95353 3.62109 8.11408 3.62109 8.31212V9.68788C3.62109 9.88592 3.46054 10.0465 3.2625 10.0465C3.06446 10.0465 2.90391 9.88592 2.90391 9.68788V8.31212Z" fill="#636363" />
      </g>
      <defs>
        <clipPath id="clip0_3381_3257">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconSoundWave;
