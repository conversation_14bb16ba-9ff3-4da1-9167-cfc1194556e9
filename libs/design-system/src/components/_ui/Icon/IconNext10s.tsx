import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconNext10s: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 18 19"
      fill="none"
      aria-label={alt}
    >
      <g id="icon / 10s for" clipPath="url(#clip0_2021_546)">
        <path
          id="Union"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.623 1.79205C10.1562 -0.278855 5.59644 0.169328 2.63231 3.13372C-0.877438 6.64422 -0.877438 12.3564 2.63231 15.8669C4.08648 17.3191 5.99352 18.2302 8.03681 18.4489C10.0801 18.6675 12.1367 18.1807 13.8652 17.0691C15.5937 15.9576 16.8901 14.2882 17.5391 12.3381C18.1881 10.388 18.1507 8.27457 17.4332 6.34866C17.3959 6.24761 17.339 6.15492 17.2658 6.0759C17.1926 5.99687 17.1045 5.93308 17.0067 5.88816C16.9088 5.84324 16.803 5.81809 16.6953 5.81415C16.5877 5.81021 16.4804 5.82755 16.3795 5.86519C16.2785 5.90282 16.186 5.96 16.1073 6.03346C16.0285 6.10692 15.965 6.1952 15.9204 6.29325C15.8758 6.3913 15.851 6.49719 15.8474 6.60485C15.8438 6.71251 15.8615 6.81982 15.8995 6.92063C16.3913 8.24491 16.4932 9.68244 16.1932 11.0629C15.8931 12.4433 15.2038 13.7088 14.2067 14.7094C11.3343 17.5819 6.66207 17.5819 3.78967 14.7094C0.918082 11.8372 0.918082 7.16342 3.78967 4.29115C6.3272 1.75361 10.2934 1.46741 13.1587 3.42951L12.4252 3.67407C12.3233 3.70808 12.229 3.76184 12.1478 3.83229C12.0666 3.90274 12.0001 3.98849 11.9521 4.08465C11.904 4.18081 11.8754 4.28549 11.8678 4.39272C11.8602 4.49996 11.8737 4.60764 11.9077 4.70962C11.9418 4.8116 11.9955 4.90588 12.0659 4.98708C12.1364 5.06828 12.2221 5.13481 12.3183 5.18287C12.4144 5.23093 12.5191 5.25958 12.6263 5.26719C12.7335 5.27479 12.8412 5.2612 12.9431 5.22719L15.0646 4.51979C15.2512 4.4575 15.4091 4.3301 15.5095 4.16089C15.6099 3.99169 15.646 3.79198 15.6113 3.59833L15.1745 1.17013C15.1352 0.957247 15.0132 0.768582 14.8352 0.645417C14.6572 0.522253 14.4377 0.474614 14.2247 0.512925C14.0116 0.551236 13.8225 0.672377 13.6985 0.849845C13.5746 1.02731 13.5259 1.24666 13.5633 1.45989L13.623 1.79205ZM7.13869 13.3695C6.98521 13.523 6.77705 13.6093 6.56 13.6093C6.34294 13.6093 6.13478 13.523 5.98131 13.3695C5.82783 13.216 5.74161 13.0078 5.74161 12.7907V8.50664L4.93519 8.99066C4.74911 9.10163 4.52663 9.13428 4.31652 9.08145C4.10641 9.02863 3.92581 8.89463 3.81432 8.70885C3.70282 8.52306 3.66953 8.30064 3.72174 8.09034C3.77395 7.88005 3.90739 7.69903 4.09282 7.58698L6.13882 6.35919C6.26301 6.28467 6.40475 6.24445 6.54956 6.24262C6.69437 6.2408 6.83708 6.27743 6.96311 6.34879C7.08915 6.42015 7.19399 6.52368 7.26695 6.6488C7.3399 6.77393 7.37836 6.91618 7.37839 7.06103V12.7907C7.37839 13.0078 7.29216 13.216 7.13869 13.3695ZM13.9256 9.92589C13.9256 11.9914 12.6672 13.6093 11.0612 13.6093C9.45518 13.6093 8.19681 11.9914 8.19681 9.92589C8.19681 7.86036 9.45507 6.24251 11.0612 6.24251C12.6673 6.24251 13.9256 7.8604 13.9256 9.92589ZM12.2888 9.92589C12.2888 8.71982 11.6418 7.87954 11.0612 7.87954C10.4807 7.87954 9.83359 8.71985 9.83359 9.92589C9.83359 11.1319 10.4805 11.9722 11.0612 11.9722C11.6418 11.9722 12.2888 11.132 12.2888 9.92589Z"
          fill="#636363"
        />
      </g>
      <defs>
        <clipPath id="clip0_2021_546">
          <rect
            width="18"
            height="18"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconNext10s;
