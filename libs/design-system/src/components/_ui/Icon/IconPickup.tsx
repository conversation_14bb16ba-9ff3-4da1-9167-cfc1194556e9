import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconPickup: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 32 32"
      fill="none"
      aria-label={alt}
    >
      <g
        id="icon / Phone"
        clipPath="url(#clip0_2031_6320)"
      >
        <path
          id="Vector"
          d="M10.3063 1.53744C9.825 0.374944 8.55625 -0.243806 7.34375 0.0874444L1.84375 1.58744C0.75625 1.88744 0 2.87494 0 3.99994C0 19.4624 12.5375 31.9999 28 31.9999C29.125 31.9999 30.1125 31.2437 30.4125 30.1562L31.9125 24.6562C32.2438 23.4437 31.625 22.1749 30.4625 21.6937L24.4625 19.1937C23.4438 18.7687 22.2625 19.0624 21.5688 19.9187L19.0438 22.9999C14.6438 20.9187 11.0813 17.3562 9 12.9562L12.0813 10.4374C12.9375 9.73744 13.2313 8.56244 12.8063 7.54369L10.3063 1.54369V1.53744Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_2031_6320">
          <rect
            width="32"
            height="32"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconPickup;
