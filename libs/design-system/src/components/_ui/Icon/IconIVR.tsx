import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconIVR: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 32 32"
      aria-label={alt}
    >
      <path fillRule="evenodd" clipRule="evenodd" d="M30.2929 19.7071C30.4804 19.8946 30.7348 20 31 20C31.2652 20 31.5196 19.8946 31.7071 19.7071C31.8946 19.5195 32 19.2652 32 19V17C32 11.3026 27.364 6.66662 21.6667 6.66662H13.3333V2.33329C13.3333 1.45596 12.2773 1.00529 11.6427 1.61062L4.30933 8.61062C4.21155 8.70388 4.13372 8.81602 4.08053 8.94023C4.02735 9.06445 3.99993 9.19817 3.99993 9.33329C3.99993 9.46841 4.02735 9.60213 4.08053 9.72635C4.13372 9.85056 4.21155 9.9627 4.30933 10.056L11.6427 17.056C12.2747 17.6586 13.3333 17.2133 13.3333 16.3333V12H23C24.8559 12.0021 26.6351 12.7403 27.9474 14.0525C29.2597 15.3648 29.9979 17.1441 30 19C30 19.2652 30.1054 19.5195 30.2929 19.7071ZM18.6667 25.3333V29.6666C18.6667 30.544 19.7213 30.9946 20.3573 30.3893L27.6907 23.3893C27.7884 23.296 27.8663 23.1839 27.9195 23.0597C27.9726 22.9355 28.0001 22.8018 28.0001 22.6666C28.0001 22.5315 27.9726 22.3978 27.9195 22.2736C27.8663 22.1494 27.7884 22.0372 27.6907 21.944L20.3573 14.944C20.2157 14.8088 20.0376 14.7181 19.8449 14.6832C19.6523 14.6483 19.4536 14.6707 19.2736 14.7476C19.0936 14.8246 18.9401 14.9527 18.8322 15.116C18.7243 15.2794 18.6668 15.4709 18.6667 15.6666V20H9C7.14413 19.9979 5.36489 19.2597 4.05259 17.9474C2.7403 16.6351 2.00212 14.8558 2 13C2 12.7348 1.89464 12.4804 1.70711 12.2929C1.51957 12.1053 1.26522 12 1 12C0.734784 12 0.48043 12.1053 0.292893 12.2929C0.105357 12.4804 0 12.7348 0 13V15C0 20.6973 4.636 25.3333 10.3333 25.3333H18.6667Z" fill="currentColor" />
    </svg>
  );
};

export default IconIVR;
