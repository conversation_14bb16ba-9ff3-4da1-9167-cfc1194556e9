import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconTranscript: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 21 21"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_2871_23)">
        <path
          d="M17.5703 2.09998H3.42969C1.81426 2.09998 0.5 3.41423 0.5 5.02966V15.8451C0.5 17.4605 1.81426 18.7748 3.42969 18.7748H17.5703C19.1857 18.7748 20.5 17.4605 20.5 15.8451V5.02966C20.5 3.41423 19.1857 2.09998 17.5703 2.09998ZM4.4943 11.0233C4.17066 11.0233 3.90836 10.761 3.90836 10.4374C3.90836 10.1138 4.17066 9.85146 4.4943 9.85146H12.7263C13.0499 9.85146 13.3122 10.1137 13.3122 10.4374C13.3122 10.7611 13.0499 11.0233 12.7263 11.0233H4.4943ZM6.98973 12.9839C6.98973 13.3074 6.72742 13.5698 6.40379 13.5698H4.4943C4.17066 13.5698 3.90836 13.3074 3.90836 12.9839C3.90836 12.6603 4.17066 12.3979 4.4943 12.3979H6.40379C6.72742 12.3979 6.98973 12.6603 6.98973 12.9839ZM3.90836 7.89087C3.90836 7.5674 4.17066 7.30494 4.4943 7.30494H6.40379C6.72742 7.30494 6.98973 7.5674 6.98973 7.89087C6.98973 8.21451 6.72742 8.47681 6.40379 8.47681H4.4943C4.17066 8.47681 3.90836 8.21451 3.90836 7.89087ZM8.27375 12.398H16.5057C16.8293 12.398 17.0916 12.6603 17.0916 12.984C17.0916 13.3074 16.8293 13.5699 16.5057 13.5699H8.27375C7.95012 13.5699 7.68781 13.3074 7.68781 12.984C7.68781 12.6603 7.95012 12.398 8.27375 12.398ZM16.5057 11.0234H14.5962C14.2726 11.0234 14.0103 10.7611 14.0103 10.4374C14.0103 10.1138 14.2726 9.8515 14.5962 9.8515H16.5057C16.8293 9.8515 17.0916 10.1138 17.0916 10.4374C17.0916 10.7611 16.8293 11.0234 16.5057 11.0234ZM16.5057 8.47681H8.27375C7.95012 8.47681 7.68781 8.21451 7.68781 7.89087C7.68781 7.5674 7.95012 7.30494 8.27375 7.30494H16.5057C16.8293 7.30494 17.0916 7.5674 17.0916 7.89087C17.0916 8.21451 16.8293 8.47681 16.5057 8.47681Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_2871_23">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0.5 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconTranscript;
