import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconMute: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 32 32"
      aria-label={alt}
    >
      <g clipPath="url(#clip0_1058_2041)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M16.0096 25.5582C16.0097 25.8341 15.9312 26.1043 15.7832 26.3372C15.6353 26.5701 15.424 26.756 15.1742 26.8731C14.9246 26.9905 14.6466 27.0344 14.373 26.9997C14.0993 26.9649 13.8412 26.8529 13.6288 26.6767L5.68439 20.0936H1.45317C1.26234 20.0937 1.07336 20.0562 0.897038 19.9832C0.720713 19.9102 0.560497 19.8032 0.425544 19.6683C0.290591 19.5334 0.183548 19.3732 0.110531 19.1969C0.037514 19.0206 -4.50241e-05 18.8316 4.05044e-08 18.6408V12.8511C4.05044e-08 12.4657 0.153091 12.096 0.425603 11.8235C0.698115 11.5509 1.06773 11.3977 1.45317 11.3976H5.68473L13.6292 4.8145C13.8414 4.6382 14.0996 4.52614 14.3733 4.49147C14.6471 4.4568 14.925 4.50097 15.1745 4.6188C15.4243 4.73603 15.6354 4.92195 15.7834 5.1548C15.9313 5.38766 16.0099 5.65782 16.01 5.9337L16.0096 25.5582ZM26.6667 13.8079L23.6111 10.6454C23.0944 10.1049 22.25 10.1106 21.7278 10.6454C21.2056 11.1801 21.2056 12.0541 21.7278 12.5946L24.7833 15.7571L21.7278 18.9196C21.2056 19.4544 21.2111 20.3284 21.7278 20.8689C22.2444 21.4094 23.0889 21.4094 23.6111 20.8689L26.6667 17.7064L29.7222 20.8689C30.2389 21.4094 31.0833 21.4036 31.6056 20.8689C32.1278 20.3341 32.1278 19.4601 31.6056 18.9196L28.55 15.7571L31.6056 12.5946C32.1278 12.0599 32.1222 11.1859 31.6056 10.6454C31.0889 10.1049 30.2444 10.1049 29.7222 10.6454L26.6667 13.8079Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1058_2041">
          <rect
            width="32"
            height="32"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconMute;
