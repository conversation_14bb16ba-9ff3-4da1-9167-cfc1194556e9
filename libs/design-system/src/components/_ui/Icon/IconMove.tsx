import { cn } from '@cdss-modules/design-system/lib/utils';
import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconMove: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 16 16"
    >
      <g clipPath="url(#clip0_2966_5487)">
        <path
          d="M15.8467 8.37906L13.5464 10.7685C13.4702 10.8453 13.373 10.8979 13.267 10.9197C13.161 10.9415 13.0509 10.9316 12.9506 10.8911C12.8502 10.8506 12.764 10.7814 12.7028 10.6922C12.6416 10.6029 12.6081 10.4976 12.6065 10.3894V9.35969L9.35484 9.35509V12.6018L10.3891 12.6064C10.4974 12.6079 10.6028 12.6414 10.6922 12.7026C10.7815 12.7638 10.8508 12.85 10.8913 12.9505C10.9319 13.0509 10.9418 13.1611 10.92 13.2671C10.8981 13.3732 10.8454 13.4705 10.7685 13.5467L8.37881 15.8467C8.27723 15.945 8.14138 16 8 16C7.85861 16 7.72277 15.945 7.62119 15.8467L5.23179 13.5464C5.15507 13.4702 5.10253 13.373 5.08077 13.267C5.05901 13.1611 5.06899 13.0511 5.10946 12.9508C5.14992 12.8504 5.21908 12.7643 5.30827 12.7031C5.39746 12.6419 5.50272 12.6084 5.61088 12.6067H6.64055L6.64462 9.35509H3.39815L3.39355 10.3894C3.39204 10.4977 3.35859 10.6031 3.29738 10.6924C3.23618 10.7818 3.14995 10.8511 3.04951 10.8916C2.94908 10.9321 2.83892 10.9421 2.73285 10.9202C2.62678 10.8984 2.52952 10.8457 2.45329 10.7687L0.153315 8.37906C0.0549794 8.27747 0 8.14163 0 8.00024C0 7.85886 0.0549794 7.72301 0.153315 7.62143L2.45356 5.23203C2.52979 5.15521 2.627 5.10259 2.733 5.08078C2.839 5.05897 2.94908 5.06892 3.04945 5.1094C3.14981 5.14988 3.236 5.21908 3.29722 5.30832C3.35843 5.39757 3.39194 5.5029 3.39355 5.61111V6.64079L6.64516 6.64486V3.39838L5.61088 3.39377C5.50259 3.39227 5.39715 3.35881 5.30781 3.29761C5.21846 3.2364 5.14918 3.15017 5.10866 3.04974C5.06814 2.94931 5.05818 2.83914 5.08004 2.73307C5.1019 2.627 5.1546 2.52974 5.23152 2.45351L7.62119 0.153528C7.72271 0.0550658 7.85857 0 8 0C8.14142 0 8.27729 0.0550658 8.37881 0.153528L10.7682 2.45378C10.845 2.53001 10.8976 2.62722 10.9194 2.73322C10.9413 2.83922 10.9313 2.9493 10.8908 3.04967C10.8504 3.15004 10.7812 3.23623 10.6919 3.29744C10.6027 3.35865 10.4973 3.39216 10.3891 3.39377H9.35944L9.35484 6.6454H12.6016L12.6065 5.61111C12.6081 5.50288 12.6416 5.39754 12.7028 5.30827C12.7641 5.21901 12.8503 5.14979 12.9506 5.10929C13.051 5.06879 13.1611 5.05881 13.2672 5.08059C13.3732 5.10238 13.4704 5.15496 13.5467 5.23176L15.8467 7.62143C15.945 7.72301 16 7.85886 16 8.00024C16 8.14163 15.945 8.27747 15.8467 8.37906Z"
          // fill="#C6C6C6"
          className={cn(`fill-[#C6C6C6] group-active:fill-black`)}
        />
      </g>
      <defs>
        <clipPath id="clip0_2966_5487">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconMove;
