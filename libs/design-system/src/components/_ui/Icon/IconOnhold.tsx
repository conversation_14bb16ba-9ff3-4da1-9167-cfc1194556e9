import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconOnhold: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 14 14"
      fill="none"
      aria-label={alt}
    >
      <path
        d="M2 7.264C2 6.312 2.238 5.43 2.70933 4.62267C3.18067 3.81533 3.82 3.176 4.62733 2.70467C5.43467 2.23333 6.312 2 7.25933 2C7.96867 2 8.65 2.14 9.29867 2.41533C9.94733 2.69067 10.5027 3.06867 10.974 3.53533C11.4453 4.002 11.8187 4.562 12.094 5.21533C12.3693 5.86867 12.5093 6.54533 12.5093 7.264C12.5093 7.97333 12.3693 8.65467 12.094 9.30333C11.8187 9.952 11.4407 10.512 10.974 10.9787C10.5073 11.4453 9.94733 11.8187 9.29867 12.094C8.65 12.3693 7.97333 12.5093 7.25933 12.5093C6.54533 12.5093 5.85933 12.3693 5.21067 12.094C4.562 11.8187 4.002 11.4407 3.53067 10.974C3.05933 10.5073 2.69067 9.94733 2.41067 9.30333C2.13067 8.65933 2 7.978 2 7.264ZM3.15733 7.264C3.15733 8.37 3.55867 9.33133 4.366 10.148C5.17333 10.9553 6.13467 11.3567 7.25933 11.3567C7.99667 11.3567 8.68267 11.1747 9.308 10.806C9.93333 10.4373 10.4373 9.94267 10.806 9.31267C11.1747 8.68267 11.3613 8.00133 11.3613 7.264C11.3613 6.52667 11.1747 5.84067 10.806 5.21067C10.4373 4.58067 9.938 4.08133 9.308 3.71267C8.678 3.344 7.99667 3.162 7.25933 3.162C6.522 3.162 5.836 3.344 5.21067 3.71267C4.58533 4.08133 4.08133 4.58067 3.708 5.21067C3.33467 5.84067 3.15733 6.52667 3.15733 7.264ZM6.85333 7.264C6.85333 7.36667 6.89067 7.46 6.96533 7.53933C7.04 7.61867 7.13333 7.656 7.24067 7.656C7.34333 7.656 7.43667 7.61867 7.516 7.53933C7.59533 7.46 7.63267 7.37133 7.63267 7.264V4.156C7.63267 4.04867 7.59533 3.95533 7.516 3.88067C7.43667 3.806 7.348 3.76867 7.24067 3.76867C7.13333 3.76867 7.04 3.806 6.96533 3.88067C6.89067 3.95533 6.85333 4.04867 6.85333 4.156V7.264Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default IconOnhold;
