import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconSave: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 21 21"
    >
      <g clipPath="url(#clip0_2231_6636)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.2926 0L7.69263 0V5.59803H13.2926V0ZM5.59263 13.9951H15.3926C15.5783 13.9951 15.7563 14.0688 15.8876 14.2C16.0189 14.3313 16.0926 14.5092 16.0926 14.6948V20.9926H4.89263V14.6948C4.89263 14.5092 4.96638 14.3313 5.09765 14.2C5.22893 14.0688 5.40698 13.9951 5.59263 13.9951ZM16.597 0.210304L20.797 4.40883C20.8619 4.47421 20.9132 4.55176 20.948 4.63701C20.9829 4.72227 21.0005 4.81356 21 4.90565V18.9007C21 19.4575 20.7787 19.9915 20.3849 20.3851C19.9911 20.7788 19.4569 21 18.9 21H17.5V14.7022C17.5 14.1455 17.2787 13.6115 16.8849 13.2178C16.4911 12.8241 15.9569 12.6029 15.4 12.6029H5.6C5.04304 12.6029 4.5089 12.8241 4.11507 13.2178C3.72125 13.6115 3.5 14.1455 3.5 14.7022V21H2.1C1.54304 21 1.0089 20.7788 0.615075 20.3851C0.221249 19.9915 0 19.4575 0 18.9007L0 2.10664C0 1.54988 0.221249 1.01592 0.615075 0.622235C1.0089 0.228547 1.54304 0.00737573 2.1 0.00737573L6.3 0.00737573V5.60541C6.3 5.97658 6.4475 6.33255 6.71005 6.59501C6.9726 6.85747 7.32869 7.00492 7.7 7.00492H13.3C13.6713 7.00492 14.0274 6.85747 14.2899 6.59501C14.5525 6.33255 14.7 5.97658 14.7 5.60541V0.00737573L16.1 0.00737573C16.1921 0.0068433 16.2834 0.0244946 16.3687 0.0593176C16.454 0.0941406 16.5316 0.14545 16.597 0.210304Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_2231_6636">
          <rect width="21" height="21" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconSave;
