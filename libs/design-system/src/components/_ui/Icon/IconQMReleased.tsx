interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconQMReleased: React.FC<IconProps> = ({
  size,
  alt,
  className,
  color = '#59C671',
}) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 54 54"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        opacity="0.5"
        d="M27 0.686523C12.0886 0.686523 0 12.5395 0 27.1615C0 41.7829 12.0886 53.6365 27 53.6365C41.9114 53.6365 54 41.7829 54 27.1615C54 12.5395 41.9114 0.686523 27 0.686523ZM27 1.88993C41.2339 1.88993 52.7727 13.2044 52.7727 27.1615C52.7727 41.1187 41.2339 52.4331 27 52.4331C12.7661 52.4331 1.22727 41.1187 1.22727 27.1615C1.22727 13.2044 12.7661 1.88993 27 1.88993ZM46.2805 30.2591C45.6618 34.0287 43.9437 37.5423 41.3336 40.3756C38.7235 43.2089 35.3329 45.241 31.571 46.2265C27.9201 47.1891 24.0669 47.1263 20.4506 46.0453C16.8344 44.9643 13.6008 42.9086 11.1185 40.1126L10.943 39.9116L10.476 40.2979C13.0244 43.2469 16.3755 45.4245 20.1384 46.5765C23.9012 47.7285 27.9206 47.8075 31.7275 46.8042C35.5585 45.8007 39.0169 43.7436 41.6943 40.8755C44.3718 38.0074 46.1567 34.4481 46.8383 30.6177L46.8837 30.353L46.2805 30.2591ZM39.4611 31.8097C38.6644 33.9234 37.3617 35.8187 35.6626 37.336C33.9634 38.8533 31.9175 39.9483 29.6969 40.529C27.4726 41.1161 25.1369 41.1696 22.887 40.685C20.6372 40.2004 18.5393 39.192 16.7707 37.7449L16.3786 38.2046C18.2225 39.7131 20.4096 40.7643 22.7552 41.2694C25.1007 41.7745 27.5357 41.7187 29.8546 41.1066C32.1267 40.5126 34.224 39.4019 35.9765 37.8645C37.7289 36.3271 39.0871 34.4063 39.941 32.258L40.0349 32.0173L39.4617 31.8103L39.4611 31.8097ZM9.73411 28.0412L8.57434 28.3457L8.05091 34.4572L8.04293 35.1323L8.01041 35.1407L7.6588 34.5601L4.08927 29.5244L2.92398 29.8307L7.9337 36.6119L8.85416 36.3712L9.73411 28.0412ZM15.6023 26.4996L10.7791 27.7668L12.844 35.3224L17.7273 34.0396L17.5089 33.2399L13.6982 34.2412L12.9594 31.5377L16.2147 30.6827L15.995 29.8782L12.7397 30.7332L12.0715 28.2891L15.8226 27.3035L15.6023 26.4996ZM19.8266 25.3985L19.6431 25.4437L16.9321 26.1555L18.9957 33.7063L20.0678 33.4241L19.1731 30.1496L21.0078 29.6682C21.4288 29.5575 21.7982 29.5936 22.1167 29.7765C22.4032 29.942 22.6064 30.1977 22.7254 30.5449L22.7622 30.664L22.9506 31.3542C23.0169 31.5967 23.101 31.8265 23.2034 32.0444C23.2912 32.2309 23.4047 32.3675 23.5452 32.4535L23.617 32.492L24.7222 32.2014L24.6884 32.0768C24.5177 31.9907 24.376 31.858 24.2804 31.6948C24.1908 31.5453 24.1191 31.3862 24.0668 31.2206L24.0257 31.0828L23.8318 30.3722C23.7091 29.9258 23.5121 29.5762 23.2384 29.3234C22.9653 29.0707 22.5929 28.9341 22.1204 28.9143C22.4806 28.6375 22.7279 28.3282 22.861 27.9853C22.9948 27.6423 23.0089 27.2771 22.9034 26.8896C22.7119 26.1904 22.3382 25.718 21.7804 25.4713C21.2631 25.2427 20.6114 25.2186 19.8266 25.3985ZM25.2106 23.9749L24.1331 24.2577L26.1974 31.8139L27.2755 31.5311L25.2106 23.9749ZM32.146 22.1529L27.1755 23.4586L29.2404 31.0142L30.313 30.7326L29.3999 27.3908L32.7424 26.5123L32.5221 25.7084L29.1803 26.5863L28.4684 23.9809L32.3656 22.9574L32.146 22.1529ZM34.5146 21.5308L33.4364 21.8136L35.5007 29.3698L36.5789 29.0864L34.5146 21.5308ZM21.168 26.2746C21.492 26.4401 21.7117 26.7355 21.8277 27.1609C21.9504 27.6104 21.9087 27.9804 21.7025 28.2699C21.4957 28.5599 21.0888 28.7843 20.4832 28.9438L18.9534 29.3457L18.2232 26.6729L19.8616 26.2427C20.4095 26.0983 20.8446 26.1091 21.168 26.2746ZM41.302 19.7473L36.4788 21.0145L38.5437 28.5701L43.427 27.2873L43.2086 26.4876L39.3979 27.4889L38.6591 24.7854L41.9144 23.9304L41.6948 23.1259L38.4394 23.9809L37.7712 21.5368L41.5223 20.5512L41.302 19.7473ZM45.3164 18.6961L45.1237 18.7431L42.6305 19.3983L44.6954 26.9539L47.1886 26.2987C48.2226 26.0267 48.9725 25.5062 49.437 24.7373C49.8727 24.0164 49.988 23.2089 49.7837 22.316L49.7383 22.1355L49.456 21.103C49.1934 20.1414 48.6644 19.4398 47.8685 18.9982C47.123 18.5836 46.2725 18.4831 45.3164 18.6961ZM9.32666 15.5498C7.189 18.547 5.9142 22.0534 5.63625 25.7006L5.61293 26.0544L6.22289 26.0869C6.42921 22.5425 7.60614 19.1182 9.6298 16.1744L9.82739 15.8916L9.32666 15.5498ZM47.266 19.7822C47.7722 20.1041 48.1281 20.5813 48.3331 21.2131L48.3809 21.3743L48.6657 22.4177C48.862 23.1373 48.8129 23.7824 48.5153 24.354C48.2183 24.9256 47.7035 25.3071 46.9702 25.4996L45.549 25.8727L43.9229 19.9206L45.3434 19.5476C46.0767 19.355 46.718 19.4332 47.266 19.7822ZM22.1222 12.8085C19.3749 13.528 16.8933 14.9996 14.9675 17.0512C13.0417 19.1028 11.7514 21.6495 11.2473 24.3937L11.2038 24.6422L11.807 24.7373C12.251 22.0568 13.4757 19.5593 15.3337 17.5455C17.1916 15.5317 19.6037 14.0875 22.2793 13.3867C24.9522 12.6824 27.7757 12.7502 30.4102 13.582C33.0448 14.4137 35.3782 15.974 37.1299 18.0752L37.287 18.2671L37.7675 17.8971C35.9529 15.6414 33.5035 13.9572 30.7225 13.0529C27.9415 12.1487 24.9511 12.0641 22.1216 12.8097L22.1222 12.8085ZM34.5348 8.46656L34.2937 9.01652C37.6686 10.4259 40.5878 12.7109 42.7373 15.6256L42.9429 15.9096L43.4448 15.5685C41.2141 12.4275 38.1262 9.96623 34.5348 8.46656ZM10.816 11.1369L10.7435 12.5317L9.64084 13.4457L11.0338 13.9734L11.6499 15.2508L12.5863 14.1894L14.0621 14.0708L13.2466 12.8807L13.5454 11.5262L12.1064 11.8614L10.816 11.1369ZM19.2737 5.78356L18.9313 7.1392L17.6703 7.83116L18.9344 8.60857L19.2903 9.97805L20.4163 9.11099L21.8878 9.26984L21.3196 7.9497L21.8768 6.67529L20.3991 6.73546L19.2737 5.78356ZM29.7215 5.5507L28.9728 6.81127L27.5553 7.22645L28.5052 8.25296L28.4058 9.68321L29.7448 9.06225L31.0966 9.52255L30.9788 8.11336L31.9158 6.97373L30.4953 6.72343L29.7215 5.5507Z"
        fill={color}
      />
    </svg>
  );
};

export default IconQMReleased;
