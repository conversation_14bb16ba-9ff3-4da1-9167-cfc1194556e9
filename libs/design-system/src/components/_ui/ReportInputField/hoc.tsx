import DateTimeField from './components/DateTimeField';
import DateRangeField from './components/DateRangeField';
import SelectField from './components/SelectField';
import TextInputField from './components/TextInputField';
import { InputComponentProps } from './types';
import DateField from './components/DateField';

// 高阶组件
export const withDynamicInput = (Component: React.FC<InputComponentProps>) => {
  const WrappedComponent = (props: InputComponentProps) => {
    const renderInput = () => {
      switch (props.type) {
        case 'text':
          return <TextInputField {...props} />;
        case 'select':
          return <SelectField {...props} />;
        case 'dateRange':
          return <DateRangeField {...props} />;
        case 'datetime':
          return <DateTimeField {...props} />;
        case 'date':
          return <DateField {...props} />;
        default:
          return null;
      }
    };

    return <Component {...props}>{renderInput()}</Component>;
  };

  WrappedComponent.displayName = `WithDynamicInput(${Component.displayName || Component.name || 'Component'})`;

  return WrappedComponent;
};
