import { InputComponentProps } from '../types';
import { useTranslation } from 'react-i18next';
import IconTriangleDown from '../../Icon/IconTriangleDown';
import { useState } from 'react';

const SelectField = (props: InputComponentProps) => {
  const { i18n } = useTranslation();
  const [showOptions, setShowOptions] = useState(false);

  return (
    <div className="relative">
      <input
        className="w-full h-full outline-none ring-0 p-2 border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500 focus:border-primary"
        value={
          props.value === '0'
            ? ''
            : i18n.language === 'en'
              ? props.options?.find((option) => option.value === props.value)
                  ?.label
              : props.options?.find((option) => option.value === props.value)
                  ?.label
        }
        disabled={!props.value}
        readOnly={true}
        placeholder={
          i18n.language === 'en' ? 'please select option' : '請選擇選項'
        }
        onFocus={() => setShowOptions(true)}
        onBlur={() => setShowOptions(false)}
        onChange={(e) => props.onChange(e.target.value)}
      />
      <span className="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
        <IconTriangleDown size="15px" />
      </span>
      {showOptions && (
        <ul className="absolute z-10 w-full bg-white border border-primary rounded-md mt-1">
          {props.options && props.options.length > 0 ? (
            props.options.map((option) => (
              <li
                key={option.value}
                className="p-2 hover:bg-grey-200 cursor-pointer"
                onMouseDown={() => props.onChange(option.value)}
              >
                {i18n.language === 'en' ? option.label : option.label}
              </li>
            ))
          ) : (
            <li
              className="p-2 text-center w-full"
              onMouseDown={(e) => e.preventDefault()}
            >
              No Data
            </li>
          )}
        </ul>
      )}
    </div>
  );
  // return (
  //   <select
  //     className="w-full"
  //     value={props.value}
  //     onChange={(e) => props.onChange(e.target.value)}
  //   >
  //     {props.options?.map((option, index) => (
  //       <option
  //         key={index}
  //         value={option}
  //       >
  //         {option}
  //       </option>
  //     ))}
  //   </select>
  // );
};

export default SelectField;
