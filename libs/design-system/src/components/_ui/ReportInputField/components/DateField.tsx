import { InputComponentProps } from '../types';
import dayjs from 'dayjs';

const DateField = (props: InputComponentProps) => {
  return (
    <div>
      <input
        className="w-full h-full outline-none ring-0 p-[0.325rem] border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500"
        type="date"
        value={props.value}
        onChange={(e) =>
          props.onChange(dayjs(e.target.value).format('YYYY-MM-DD'))
        }
      />
    </div>
  );
};

export default DateField;
