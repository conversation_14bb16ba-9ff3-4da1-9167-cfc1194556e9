import { InputComponentProps } from '../types';

const DateRangeField = (props: InputComponentProps) => {
  return (
    <div>
      <input
        type="date"
        value={props.value?.split(' - ')[0] || ''}
        onChange={(e) =>
          props.onChange(
            `${e.target.value} - ${props.value?.split(' - ')[1] || ''}`
          )
        }
      />
      <span> to </span>
      <input
        type="date"
        value={props.value?.split(' - ')[1] || ''}
        onChange={(e) =>
          props.onChange(
            `${props.value?.split(' - ')[0] || ''} - ${e.target.value}`
          )
        }
      />
    </div>
  );
};

export default DateRangeField;
