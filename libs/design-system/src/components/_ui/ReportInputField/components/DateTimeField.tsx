import { InputComponentProps } from '../types';
import dayjs from 'dayjs';

const DateTimeField = (props: InputComponentProps) => {
  return (
    <div>
      <input
        className="w-full h-full outline-none ring-0 p-[0.325rem] border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500"
        type="datetime-local"
        value={props.value}
        onChange={(e) =>
          props.onChange(dayjs(e.target.value).format('YYYY-MM-DD HH:mm:ss'))
        }
      />
    </div>
  );
};

export default DateTimeField;
