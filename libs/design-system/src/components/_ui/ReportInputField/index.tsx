import { withDynamicInput } from './hoc';
import { InputComponentProps } from './types';

// 基础输入组件
const BaseInputComponent: React.FC<InputComponentProps> = ({
  label,
  isRequired,
  children,
  error,
}) => (
  <div className="flex flex-col gap-1">
    <label>
      {label}
      {isRequired && <span className="text-red-500">*</span>}
    </label>
    <section>{children}</section>
    {error && <p className="text-red-500">{error}</p>}
  </div>
);

// 使用高阶组件
const DynamicInputComponent = withDynamicInput(BaseInputComponent);

export default DynamicInputComponent;
