import { cn } from '../../../lib/utils';
import ReactDatePicker from 'react-datepicker';
import Icon from '../Icon';
import Input from '../Input';
import dayjs from 'dayjs';
import Button from '../Button';
import { useState } from 'react';

export type TDatePickerProps = {
  date: Date | null;
  onChange: (date: Date | null) => void;
  disabled?: boolean;
  showTimeSelect?: boolean;
  showPopperArrow?: boolean;
};

export const DatePicker = ({
  date,
  onChange,
  disabled,
  showTimeSelect = false,
  showPopperArrow = false,
}: TDatePickerProps) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const customContainer = ({ className, children }: any) => {
    return (
      <div
        className={cn(
          'bg-white p-2 flex flex-col w-full h-full border-none shadow-card',
          className
        )}
      >
        {children}
        <div className="w-full flex justify-between items-center border-grey-200 border-t pt-3 px-3 pb-1">
          <Button
            variant="blank"
            bodyClassName="px-2 py-1  min-w-[61px]"
            onClick={() => onChange(null)}
          >
            Clear
          </Button>
          <Button
            variant="primary"
            bodyClassName="px-2 py-1 min-w-[61px]"
            onClick={() => {
              onChange(date);
              setIsOpen(!isOpen);
            }}
          >
            Apply
          </Button>
        </div>
      </div>
    );
  };

  const renderDayContents = (day: any, date: any) => {
    return (
      <div
        id="renderday"
        className="w-[25px] h-[25px] rounded-full flex items-center justify-center hover:border hover:border-black active:border-none"
      >
        {day}
      </div>
    );
  };

  return (
    <div className="w-full *:w-full">
      <ReactDatePicker
        disabled={disabled}
        dateFormat={showTimeSelect ? 'yyyy-MM-dd HH:mm:ss' : 'yyyy-MM-dd'}
        selected={date}
        onChange={(date) => onChange(date)}
        showTimeSelect={showTimeSelect}
        placeholderText="YYYY/MM/DD"
        calendarClassName="custom-calendar"
        calendarContainer={customContainer}
        formatWeekDay={(day: string) => day.slice(0, 3)}
        renderDayContents={renderDayContents}
        dayClassName={() => 'day'}
        showMonthDropdown
        showYearDropdown
        showPopperArrow={showPopperArrow}
        shouldCloseOnSelect={false}
        open={isOpen}
        onInputClick={() => setIsOpen(!isOpen)}
        onClickOutside={() => setIsOpen(false)}
        fixedHeight
        renderCustomHeader={({
          date,
          decreaseMonth,
          increaseMonth,
          prevMonthButtonDisabled,
          nextMonthButtonDisabled,
        }) => {
          return (
            <div className="flex flex-col justify-between items-center w-full bg-white">
              <div className="w-full flex justify-between items-center">
                <button
                  onClick={decreaseMonth}
                  disabled={prevMonthButtonDisabled}
                >
                  <Icon
                    name="arrow-left"
                    size={16}
                  />
                </button>
                <div>
                  {date
                    ? dayjs(date).format('YYYY - MMM')
                    : new Date().getMonth()}
                </div>
                <button
                  onClick={increaseMonth}
                  disabled={nextMonthButtonDisabled}
                >
                  <Icon
                    name="arrow-right"
                    size={16}
                  />
                </button>
              </div>
              <div className="mt-3 w-full border border-t-grey-200" />
            </div>
          );
        }}
        customInput={
          <Input
            className="border-none focus:bg-transparent"
            size={'s'}
            containerClassName=" hover:border-primary-500 focus:border-primary-900 focus:bg-common-white focus:shadow-field"
            afterIcon={<Icon name="calendar" />}
          />
        }
      />
    </div>
  );
};

export default DatePicker;
