import { cn } from '../../../lib/utils';
import ReactDatePicker from 'react-datepicker';

export type TDatePickerProps = {
  date: Date | null;
  onChange: (date: Date | null) => void;
  disabled?: boolean;
};

export const DateTimePicker = ({
  date,
  onChange,
  disabled,
}: TDatePickerProps) => {
  return (
    <div className="w-full *:w-full">
      ##
      <ReactDatePicker
        disabled={disabled}
        dateFormat={'yyyy-MM-dd HH:mm:ss'}
        calendarClassName="custom-calendar"
        className={cn(
          'block w-full h-field px-2 rounded-[4px] border-grey-200 outline-none text-body hover:border-primary-500 focus:border-primary-900 focus:bg-common-white focus:shadow-field',
          disabled
            ? 'bg-common-disable border-none'
            : 'bg-common-white border-[1px]'
        )}
        showMonthDropdown
        showYearDropdown
        selected={date}
        onChange={(date) => onChange(date)}
        showTimeSelect
      />
    </div>
  );
};

export default DateTimePicker;
