import { TabsProvider } from '../../../context/TabsContext';
import { RoleProvider } from '../../../context/RoleContext';
import { RouteProvider, IRoute } from '../../../context/RouteContext';
import { ToolbarProvider } from '../../../context/ToolbarContext';
import { ReactNode } from 'react';
import { QueryClient } from '@tanstack/react-query';
import { CDSSProvider } from '../../../context/CDSSContext';

// Create a client
const queryClient = new QueryClient();

export const PageContext = ({
  children,
  routes,
  basePath = '',
}: {
  children: ReactNode;
  routes: IRoute[];
  basePath?: string;
}) => {
  return (
    <RouteProvider
      routes={routes}
      basePath={basePath}
    >
      <RoleProvider basePath={basePath}>
        <CDSSProvider>
          <ToolbarProvider>
            <TabsProvider>{children}</TabsProvider>
          </ToolbarProvider>
        </CDSSProvider>
      </RoleProvider>
    </RouteProvider>
  );
};

export default PageContext;
