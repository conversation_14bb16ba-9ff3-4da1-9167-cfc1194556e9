import { Reorder } from 'framer-motion';
import Switch from '../Switch';
import Icon from '../../_ui/Icon';
import { ICriteria } from '../../../@types/config';
import { useTranslation } from 'react-i18next';

type TDraggableProps = {
  items: ICriteria[];
  onChange: (e: any) => void;
  toogleSwitch?: boolean;
};

export type { ICriteria };

const Draggable: React.FC<TDraggableProps> = ({
  items,
  onChange,
  toogleSwitch = false,
}) => {
  const { i18n } = useTranslation();
  const handleSwitchToggle = (id: string) => {
    const updatedArray = items.map((item: ICriteria) => {
      if (item.value === id) {
        return {
          ...item,
          active: !item.active,
        };
      }
      return item;
    });

    onChange && onChange(updatedArray);
  };

  return (
    <Reorder.Group
      axis="y"
      values={items}
      onReorder={(e) => {
        onChange && onChange(e);
      }}
      className="flex flex-col w-full h-full overflow-auto"
    >
      {items.map((p: ICriteria) => {
        return (
          <Reorder.Item
            value={p}
            key={p.value}
          >
            <div className="w-full p-4 flex flex-row items-center justify-between text-center border-b border-grey-100 group hover:bg-primary-100 active:bg-grey-300 active:border-black">
              <div className="flex flex-row items-center gap-2">
                <Icon
                  name="move"
                  className="active:fill-black"
                />
                {i18n.language === 'en' ? p.labelEn : p.labelCh}
              </div>
              {toogleSwitch && (
                <Switch
                  id={p.value}
                  disabled={
                    p.value === 'conversationStart' ||
                    p.value === 'conversationEnd'
                  }
                  isRequired={
                    p.value === 'conversationStart' ||
                    p.value === 'conversationEnd'
                  }
                  defaultChecked={
                    p.value === 'conversationStart' ||
                    p.value === 'conversationEnd'
                      ? true
                      : p.active
                  }
                  onChange={(e) => handleSwitchToggle(e.currentTarget.id)}
                />
              )}
            </div>
          </Reorder.Item>
        );
      })}
    </Reorder.Group>
  );
};

export default Draggable;
