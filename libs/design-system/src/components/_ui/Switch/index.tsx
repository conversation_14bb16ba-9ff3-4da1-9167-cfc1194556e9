import { cn } from '../../../lib/utils';
import { VariantProps, cva } from 'class-variance-authority';

const switchVariants = cva('', {
  variants: {
    size: {
      s: 'w-[44px] h-6',
      m: 'w-[68px] h-9',
      l: 'w-[90px] h-10',
    },
    activeColor: {
      green: 'peer-checked:bg-status-success',
      primary: 'peer-checked:bg-primary-500',
      secondary: 'peer-checked:bg-primary-700',
    },
  },
  defaultVariants: {
    size: 'm',
    activeColor: 'green',
  },
});

type TSwitchVariants = VariantProps<typeof switchVariants>;

type TSwitchProps = Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> &
  TSwitchVariants & {
    isRequired?: boolean;
  };

export const Switch = ({
  size = 'm',
  activeColor = 'primary',
  isRequired = false,
  ...props
}: TSwitchProps) => {
  return (
    <label className="relative flex items-center cursor-pointer">
      <input
        type="checkbox"
        {...props}
        className={cn('sr-only peer', props?.className)}
      />
      <div
        className={cn(
          'bg-grey-400 w-[68px] h-9 rounded-full',
          // outerSizeClasses,
          // activeColorClasses
          switchVariants({
            size,
            activeColor: isRequired ? 'secondary' : activeColor || 'primary',
          })
        )}
      />
      <div
        className={cn(
          'absolute transition-all duration-300 transform rounded-full bg-white',
          size === 's' && 'left-1 top-1 size-4 peer-checked:translate-x-5',
          size === 'm' && 'left-2 top-2 size-5 peer-checked:translate-x-8',
          size === 'l' && 'left-2 top-2 size-6 peer-checked:translate-x-12'
        )}
      />
    </label>
  );
};

export default Switch;
