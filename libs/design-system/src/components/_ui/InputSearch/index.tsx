import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface InputSearchProps {
  value?: string;
  placeholder?: string;
  options?: { [key: string]: string }[];
  onItemSelected: (value: { [key: string]: string }) => void;
}

const InputSearch = ({
  value = '',
  placeholder = 'Search...',
  options = [],
  onItemSelected,
}: InputSearchProps) => {
  const [showOptions, setShowOptions] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [filteredOptions, setFilteredOptions] = useState(options);
  const { i18n } = useTranslation();

  useEffect(() => {
    setFilteredOptions(
      options.filter((option) =>
        option['labelEn'].toLowerCase().includes(searchValue.toLowerCase())
      )
    );
    if (searchValue && searchValue !== '' && searchValue.length > 0) {
      setShowOptions(true);
    } else {
      setShowOptions(false);
    }
  }, [searchValue, options]);

  return (
    <div className="relative">
      <div className="relative flex items-center">
        <input
          className="w-full h-full outline-none ring-0 p-2 pr-9 border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500 focus:border-primary"
          value={searchValue}
          placeholder={placeholder}
          //   onFocus={() => setShowOptions(true)}
          //   onBlur={() => setShowOptions(false)}
          onChange={(e) => {
            setSearchValue(e.target.value);
          }}
        />
        <span className="absolute right-2 text-grey-400">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-5 h-5"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"
            />
          </svg>
        </span>
      </div>
      {showOptions && (
        <ul className="absolute z-10 w-full bg-white border border-primary rounded-md mt-1">
          {filteredOptions && filteredOptions.length > 0 ? (
            filteredOptions.map((option, index) => (
              <li
                key={index}
                className="p-2 hover:bg-grey-200 rounded-md cursor-pointer"
                onMouseDown={() => onItemSelected(option)}
              >
                {i18n.language === 'en' ? option['labelEn'] : option['labelCh']}
              </li>
            ))
          ) : (
            <li
              className="p-2 text-center w-full"
              onMouseDown={(e) => e.preventDefault()}
            >
              No Data
            </li>
          )}
        </ul>
      )}
    </div>
  );
};

export default InputSearch;
