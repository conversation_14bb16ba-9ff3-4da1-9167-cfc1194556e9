import React, { useState } from 'react';
import { FilterComponentProps, optionsMap } from './config';
import Icon from '../Icon';
import { useTranslation } from 'react-i18next';
import IconTriangleDown from '../Icon/IconTriangleDown';

const ComperationFilter: React.FC<FilterComponentProps> = ({
  labelCh,
  labelEn,
  value,
  onChange,
}) => {
  const { i18n } = useTranslation();
  const [showOptions, setShowOptions] = useState(false);
  const options = optionsMap[value.value] || [];

  return (
    <div className="flex flex-row items-center gap-2">
      <div className="flex-none inline-flex relative">
        <input
          className="appearance-none peer cursor-pointer w-[21px] h-[21px] border-2 rounded-md border-primary-500 checked:bg-primary-500 disabled:bg-grey-200 disabled:border-grey-200 checked:after:text-white"
          type="checkbox"
          checked={value.checked}
          onChange={() => onChange({ ...value, checked: !value.checked })}
        />
        <Icon
          size={11}
          name="check"
          className={
            'absolute left-[5px] top-[5px] hidden peer-checked:block text-white pointer-events-none'
          }
        />
      </div>

      <label
        className="basis-1/5 pr-6"
        style={{ textAlignLast: 'justify' }}
      >
        {i18n.language === 'en' ? labelEn : labelCh}
      </label>
      <div className="basis-1/5 relative">
        <input
          className="w-full h-full outline-none ring-0 p-2 border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500 focus:border-primary"
          value={
            i18n.language === 'en'
              ? options.find((option) => option.value === value.rule)
                  ?.labelEn ?? ''
              : options.find((option) => option.value === value.rule)
                  ?.labelCh ?? ''
          }
          disabled={!value.checked}
          placeholder={
            i18n.language === 'en' ? 'please select option' : '請選擇選項'
          }
          onFocus={() => setShowOptions(true)}
          onBlur={() => setShowOptions(false)}
          onChange={(e) => {
            onChange({ ...value, rule: e.target.value });
          }}
        />
        <span className="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
          <IconTriangleDown size="15px" />
        </span>
        {showOptions && (
          <ul className="absolute z-10 w-full bg-white border border-primary rounded-md mt-1">
            {options && options.length > 0 ? (
              options.map((option) => (
                <li
                  key={option.value}
                  className="p-2 hover:bg-grey-200 cursor-pointer"
                  onMouseDown={() =>
                    onChange({
                      ...value,
                      value: value.value,
                      rule: option.value,
                    })
                  }
                >
                  {i18n.language === 'en' ? option.labelEn : option.labelCh}
                </li>
              ))
            ) : (
              <li
                className="p-2 text-center w-full"
                onMouseDown={(e) => e.preventDefault()}
              >
                No Data
              </li>
            )}
          </ul>
        )}
      </div>
      <div className="basis-3/5">
        <input
          className="w-full h-full outline-none ring-0 p-2 border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500"
          type="text"
          value={(value?.data as string) ?? ''}
          disabled={!value.checked}
          onChange={(e) => {
            if (value.checked) {
              // 如果checkbox选中，则更新输入框内容
              onChange({ ...value, data: e.target.value });
            } else {
              // 如果checkbox没有选中，则无法更新输入框内容
              e.preventDefault();
            }
          }}
        />
      </div>
    </div>
  );
};

export default ComperationFilter;
