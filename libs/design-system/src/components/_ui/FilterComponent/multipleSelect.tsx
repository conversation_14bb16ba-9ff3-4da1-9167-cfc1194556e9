import React from 'react';
import { FilterComponentProps } from './config';
import Icon from '../Icon';
import { useTranslation } from 'react-i18next';

const MuiltiSelectFilter: React.FC<FilterComponentProps> = ({
  labelCh,
  labelEn,
  value,
  onChange,
}) => {
  const { i18n } = useTranslation();

  return (
    <div className="flex flex-row items-center gap-2">
      <div className="flex-none inline-flex relative">
        <input
          className="appearance-none peer cursor-pointer w-[21px] h-[21px] border-2 rounded-md border-primary-500 checked:bg-primary-500 disabled:bg-grey-200 disabled:border-grey-200 checked:after:text-white"
          type="checkbox"
          checked={value.checked}
          onChange={() => onChange({ ...value, checked: !value.checked })}
        />
        <Icon
          size={11}
          name="check"
          className={
            'absolute left-[5px] top-[5px] hidden peer-checked:block text-white pointer-events-none'
          }
        />
      </div>

      <label
        className="basis-1/5 pr-6"
        style={{ textAlignLast: 'justify' }}
      >
        {i18n.language === 'en' ? labelEn : labelCh}
      </label>
      <input
        className="basis-4/5 w-full h-full outline-none ring-0 p-2 border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500"
        type="text"
        value={(value.data as string) ?? ''}
        disabled={!value.checked}
        onChange={(e) => {
          if (value.checked) {
            // 如果checkbox选中，则更新输入框内容
            onChange({ ...value, data: e.target.value });
          } else {
            // 如果checkbox没有选中，则无法更新输入框内容
            e.preventDefault();
          }
        }}
      />
    </div>
  );
};

export default MuiltiSelectFilter;
