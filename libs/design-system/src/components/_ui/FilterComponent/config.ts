import InputFilter from './InputFilter';
import { Condition } from '.';
import SelectFilter from './SelectFilter';
import DateFilter from './DateFilter';
import ComperationFilter from './ComperationFilter';
import CheckboxFilter from './CheckboxFilter';
import MuiltiSelectFilter from './multipleSelect';
import DateRangeFilter from './DateRangeFilter';
import SelectFilterByApi from './SelectFilterByApi';

export interface FilterComponentProps {
  labelCh: string;
  labelEn?: string;
  value: Condition;
  onChange: (value: Condition) => void;
  onRequest?: (
    value: Condition,
    setOptions: (options: unknown) => void
  ) => void;
}

// Map filterType to components
const filterTypeToComponent: Record<string, React.FC<FilterComponentProps>> = {
  input: InputFilter,
  select: SelectFilter,
  date: DateFilter,
  compare: ComperationFilter,
  bool: CheckboxFilter,
  dateRange: DateRangeFilter,
  multipleSelect: SelectFilterByApi,
};

const mediaTypeOptions: { value: string; labelEn: string; labelCh: string }[] =
  [
    { value: '', labelEn: 'All', labelCh: '全部' },
    { value: 'voice', labelEn: 'Voice', labelCh: '語音' },
    { value: 'message', labelEn: 'Message', labelCh: '消息' },
    { value: 'chat', labelEn: 'Chat', labelCh: '聊天' },
    { value: 'email', labelEn: 'Email', labelCh: '電子郵件' },
    { value: 'callback', labelEn: 'Callback', labelCh: '回撥' },
    { value: 'whatsapp', labelEn: 'WhatsApp', labelCh: 'WhatsApp' },
  ];
const directionOptions: { value: string; labelEn: string; labelCh: string }[] =
  [
    { value: '', labelEn: 'All', labelCh: '全部' },
    { value: 'inbound', labelEn: 'Inbound', labelCh: '來電' },
    { value: 'outbound', labelEn: 'Outbound', labelCh: '撥出' },
  ];
const compareOptions: { value: string; labelEn: string; labelCh: string }[] = [
  { value: 'eq', labelEn: 'Equal', labelCh: '等於' },
  { value: 'ge', labelEn: 'Greater Than', labelCh: '大於' },
  { value: 'le', labelEn: 'Less Than', labelCh: '小於' },
];

const evaluationResultOptions: {
  value: string;
  labelEn: string;
  labelCh: string;
}[] = [
  { value: '', labelEn: 'All', labelCh: '全部' },
  { value: 'Passed', labelEn: 'Passed', labelCh: '通過' },
  { value: 'Failed', labelEn: 'Failed', labelCh: '不通過' },
];

const endedOptions: {
  value: string;
  labelEn: string;
  labelCh: string;
}[] = [
  { value: '', labelEn: 'All', labelCh: '全部' },
  { value: 'Yes', labelEn: 'Yes', labelCh: '是' },
  { value: 'No', labelEn: 'No', labelCh: '否' },
];

const userStatusOptions: { value: string; labelEn: string; labelCh: string }[] =
  [
    { value: 'active', labelEn: 'active', labelCh: '已激活' },
    { value: 'inactive', labelEn: 'inactive', labelCh: '未激活' },
  ];

const userLoginOptions: { value: string; labelEn: string; labelCh: string }[] =
  [
    { value: 'password', labelEn: 'Password', labelCh: '密码登录' },
    { value: 'ldap', labelEn: 'Ldap', labelCh: 'Ldap' },
  ];

const emailStateOptions: {
  value: string;
  labelEn: string;
  labelCh: string;
}[] = [
  { value: 'WAITING', labelEn: 'WAITING', labelCh: '全部' },
  { value: 'ALERTING', labelEn: 'ALERTING', labelCh: '是' },
  { value: 'CONNECTED', labelEn: 'CONNECTED', labelCh: '是' },
  { value: 'DISCONNECTED', labelEn: 'DISCONNECTED', labelCh: '是' },
  { value: 'WRAP-UP', labelEn: 'WRAP-UP', labelCh: '否' },
];

export const optionsMap: Record<
  string,
  { value: string; labelEn: string; labelCh: string }[]
> = {
  mediaType: mediaTypeOptions,
  direction: directionOptions,
  conversationDuration: compareOptions,
  finalResult: evaluationResultOptions,
  state: userStatusOptions,
  groupNames: [],
  roleNames: [],
  authtype: userLoginOptions,
  ended: endedOptions,
  emailState: emailStateOptions,
  // 可以继续添加其他类型及其对应的选项
};

export default filterTypeToComponent;
