import React, { useRef, useLayoutEffect, useState, useEffect } from 'react';
import * as Popover from '@radix-ui/react-popover';
import IconTriangleDown from '../Icon/IconTriangleDown';
import FilterTag from '../FilterTag';
import IconFullClose from '../Icon/IconFullClose';

interface TWhitePanelProps {
  children?: React.ReactNode;
  tags: (string | null)[];
  placeholder?: string;
}

const SearchInput = ({ children, tags, placeholder }: TWhitePanelProps) => {
  const [visibleTags, setVisibleTags] = useState<(string | null)[]>(tags);
  const [hiddenCount, setHiddenCount] = useState(0);
  const tagsContainerRef = useRef<HTMLDivElement | null>(null);
  const tagRefs = useRef<(HTMLDivElement | null)[]>([]);

  useLayoutEffect(() => {
    setVisibleTags(tags);
    if (hiddenCount !== 0 && visibleTags.length + hiddenCount > tags.length)
      setHiddenCount(0);
  }, [tags]);

  useEffect(() => {
    if (tagsContainerRef.current) {
      let totalWidth = 0;
      const visible = [];
      let hidden = 0;

      for (let i = 0; i < tags.length; i++) {
        const tagRef = tagRefs.current[i];
        if (tagRef) {
          const tagWidth = tagRef.getBoundingClientRect().width;
          if (totalWidth + tagWidth > 1150) {
            hidden = tags.length - i;
            setHiddenCount(hidden); // set the hidden count
            break;
          }
          totalWidth += tagWidth;
          visible.push(tags[i]);
        }
      }

      if (visible.length !== visibleTags.length) {
        setVisibleTags(visible);
      }
    }
  }, [visibleTags]);

  const tagsInclue = (tagStr: string) => {
    return visibleTags[0]?.includes(tagStr);
  };

  // Render the tags
  const renderTags = (placeholder?: string) => {
    if (!visibleTags) return null;
    if (visibleTags.length === 0)
      return (
        <div className="p-1 m-1 font-normal text-base leading-[21px] text-black">
          {placeholder ? placeholder : 'Filter'}
        </div>
      );
    // only show the Conversation ID item when it had been filtered
    if (tagsInclue('Conversation ID'))
      return (
        <div
          key={`tag-${0}`}
          ref={(el) => (tagRefs.current[0] = el)} // Set the ref for each tag
        >
          <FilterTag
            tagName={tags[0]}
            onDelete={() => null}
            id={`tag-${0}`}
          />
        </div>
      );
    return visibleTags.map((tag, index) => {
      if (tag) {
        return (
          <div
            key={`tag-${index}`}
            ref={(el) => (tagRefs.current[index] = el)} // Set the ref for each tag
          >
            <FilterTag
              tagName={tag}
              onDelete={() => null}
              id={`tag-${index}`}
            />
          </div>
        );
      }
    });
  };

  return (
    <Popover.Root>
      <section className="w-auto flex items-center bg-white rounded-md">
        <Popover.Trigger className="min-w-64 bg-white rounded-[4px] border-[1px] border-grey-200 data-[state=open]:border-primary-500">
          {/* SearchInput trigger */}
          <section className="w-full flex">
            {/* Render the tags */}
            <div
              ref={tagsContainerRef}
              className="flex flex-row flex-1 max-w-[1150px] overflow-hidden"
            >
              {renderTags(placeholder)}
            </div>
            {/* Hidden item count */}
            {hiddenCount > 0 && !tagsInclue('Conversation ID') && (
              <div className="flex items-center flex-shrink-0 text-sm text-black mx-2 my-1 px-2 rounded-full bg-[#FFE7B6]">
                +{hiddenCount}{' '}
                <IconFullClose
                  size="15px"
                  className="ml-1 justify-center self-center text-center"
                />
              </div>
            )}
            {/* TriangleDown Icon */}
            <div className="flex p-1 m-1 flex-shrink-0">
              <IconTriangleDown
                size="15px"
                className="justify-center self-center text-center"
              />
            </div>
          </section>
        </Popover.Trigger>
        <Popover.Portal>
          {/* SearchInput condition popover */}
          <Popover.Content
            onOpenAutoFocus={(e) => e.preventDefault()}
            align="start"
            className="mt-1 bg-white shadow-[0px_2px_10px_0px_rgba(0,0,0,0.18)] z-50"
          >
            {children}
          </Popover.Content>
        </Popover.Portal>
      </section>
    </Popover.Root>
  );
};

export default SearchInput;
