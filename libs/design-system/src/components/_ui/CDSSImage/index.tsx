// NextJS has a next/image component claiming to be optimizing image,
// Yet we experienced a lot of issues (slow loading, image not showing, etc.) while using it.
// We suspect it may be because of server speed of our testing environment.
// The investigation is in the backlog and for now we fallback to the native img tag but wrapping it with a custom component
// So that we can change back to next/image or use other methods easily in the future.
import { cn } from '../../../lib/utils';
import React from 'react';

type TCDSSImageProps = {
  fill?: boolean;
  basePath?: string;
} & React.ImgHTMLAttributes<HTMLImageElement>;

export const CDSSImage = ({
  fill,
  className,
  src,
  basePath,
  ...props
}: TCDSSImageProps) => {
  return (
    <img
      {...props}
      src={`${basePath || ''}${src}`}
      alt={props?.alt ?? ''}
      className={cn(fill && 'w-full h-full object-cover', className)}
    />
  );
};

export default CDSSImage;
