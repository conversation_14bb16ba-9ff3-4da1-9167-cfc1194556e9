'use client';

import React from 'react';
import { cn } from '../../../lib/utils';
import Icon from '../Icon';

export type TCheckboxProps = {
  testId?: string;
  className?: string;
  label?: string | React.ReactNode;
  hidden?: boolean;
} & React.InputHTMLAttributes<HTMLInputElement>;

const Checkbox: React.FC<TCheckboxProps> = ({
  label,
  hidden,
  className,
  ...props
}) => {
  return (
    <label
      htmlFor={props.id}
      className={cn(
        'inline-flex gap-2 items-start relative cursor-pointer w-full',
        hidden && 'hidden'
      )}
    >
      <div className="flex flex-row gap-2 items-center">
        <input
          type="checkbox"
          {...props}
          className="appearance-none peer cursor-pointer w-[21px] h-[21px] border-2 rounded-md border-primary-500 checked:bg-primary-500 disabled:bg-grey-200 disabled:border-grey-200 checked:after:text-white"
        />
        <Icon
          size={11}
          name="check"
          className={
            'absolute left-[5px] top-[5px] hidden peer-checked:block text-white'
          }
        />
      </div>
      {label && (
        <span className={cn(props.disabled ? 'text-grey-200' : '', className)}>
          {label}
        </span>
      )}
    </label>
  );
};

export default Checkbox;
