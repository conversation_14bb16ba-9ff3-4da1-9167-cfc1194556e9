'use client';
import {
  useRole,
  TglobalConfig,
  TUserConfig,
} from '../../../context/RoleContext';
import { useMemo } from 'react';
import Loader from '../Loader';

export type TAuthCheckerProps = {
  appConfig?: any;
  requiredPemissions?: {
    global?: TglobalConfig;
    user?: TUserConfig;
  };
  children: React.ReactNode;
  unAuthorizedComponent?: React.ReactNode;
  emptyWhenUnauthorized?: boolean;
};

const isGlobalAuthorized = (
  globalConfig?: TglobalConfig,
  requiredPermission?: TglobalConfig
) => {
  if (!globalConfig || !requiredPermission) return;
  // Check if all required portals are included in the global config portals
  const requiredPortals = requiredPermission?.portals;
  const hasPortals = requiredPortals
    ? requiredPortals?.every((portal) =>
        globalConfig?.portals?.includes(portal)
      )
    : true;

  // Check if all required services are present and active in the global config services
  const requiredServices = requiredPermission?.services;
  const hasServices = requiredServices
    ? requiredServices?.every(
        (service: any) => globalConfig?.services?.[service]?.active === true
      )
    : true;

  return hasPortals && hasServices;
};

const isUserAuthorized = (
  userConfig?: TUserConfig,
  requiredPermission?: TUserConfig
) => {
  // Check if all required permissions are included in the user config permissions
  if (!userConfig || !requiredPermission) return;
  const requiredPemissions = requiredPermission?.permissions;
  const isAuthorized = requiredPemissions
    ? requiredPemissions?.every((permission) =>
        userConfig?.permissions?.includes(permission)
      )
    : true;
  return isAuthorized;
};

export const AuthChecker = ({
  requiredPemissions,
  children,
  unAuthorizedComponent,
  emptyWhenUnauthorized,
}: TAuthCheckerProps) => {
  const { globalConfig, userConfig, loading } = useRole();

  const isAuthorized = useMemo(() => {
    if (!requiredPemissions) return true;
    return (
      isGlobalAuthorized(globalConfig, requiredPemissions?.global) &&
      isUserAuthorized(userConfig, requiredPemissions?.user)
    );
  }, [globalConfig, requiredPemissions, userConfig]);

  if (loading) {
    if (emptyWhenUnauthorized) return null;
    return (
      <div className="w-full h-full flex items-center justify-center py-12">
        <Loader size={64} />
      </div>
    );
  }

  if (!isAuthorized) {
    if (emptyWhenUnauthorized) return null;
    if (unAuthorizedComponent) return <>{unAuthorizedComponent}</>;
    return (
      <h2 className="p-6 font-bold text-t6">
        You are unauthorized to use this feature.
      </h2>
    );
  }

  return <>{children}</>;
};

export default AuthChecker;
