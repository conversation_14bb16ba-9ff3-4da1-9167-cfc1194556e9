'use client';

import * as React from 'react';
import * as SheetPrimitive from '@radix-ui/react-dialog';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '../../../lib/utils';
import Button from '../Button';
import Icon from '../Icon';

const BaseSheet = SheetPrimitive.Root;

const BaseSheetTrigger = SheetPrimitive.Trigger;

const BaseSheetClose = SheetPrimitive.Close;

const BaseSheetPortal = SheetPrimitive.Portal;

const BaseSheetOverlay = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <SheetPrimitive.Overlay
    className={cn(
      'fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
      className
    )}
    {...props}
    ref={ref}
  />
));
BaseSheetOverlay.displayName = SheetPrimitive.Overlay.displayName;

export type SideType = 'top' | 'bottom' | 'left' | 'right';

type TSheetProps = {
  side?: SideType;
  trigger?: React.ReactNode;
  headerTitle: string;
  content: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
  open?: boolean;
  onOpenChange?: () => void;
};

const Sheet: React.FC<TSheetProps> = ({
  side = 'right',
  trigger,
  headerTitle,
  content,
  footer,
  className,
  open,
  onOpenChange,
}) => {
  return (
    <BaseSheet open={open} onOpenChange={onOpenChange}>
      <BaseSheetTrigger asChild>{trigger}</BaseSheetTrigger>
      <BaseSheetContent side={side} className={className}>
        <BaseSheetHeader title={headerTitle} />
        {content}
        {footer && <BaseSheetFooter>{footer}</BaseSheetFooter>}
      </BaseSheetContent>
    </BaseSheet>
  );
};

const sheetVariants = cva(
  'fixed z-50 bg-white transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500',
  {
    variants: {
      side: {
        top: 'inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top min-h-[300px]',
        bottom:
          'inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom min-h-[300px]',
        left: 'inset-y-0 left-0 h-full w-3/4 md:max-w-[421px] border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm',
        right:
          'inset-y-0 right-0 h-full w-3/4 md:max-w-[421px] border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right',
      },
    },
    defaultVariants: {
      side: 'right',
    },
  }
);

interface SheetContentProps
  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,
    VariantProps<typeof sheetVariants> {}

const BaseSheetContent = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Content>,
  SheetContentProps
>(({ side = 'right', className, children, ...props }, ref) => (
  <BaseSheetPortal>
    <BaseSheetOverlay />
    <SheetPrimitive.Content
      ref={ref}
      className={cn(
        'relative flex flex-col',
        sheetVariants({ side }),
        className
      )}
      {...props}
    >
      {children}
    </SheetPrimitive.Content>
  </BaseSheetPortal>
));
BaseSheetContent.displayName = SheetPrimitive.Content.displayName;

const BaseSheetHeader = ({
  className,
  title,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-row justify-between items-center border-b border-grey-200 px-4 py-2.5',
      className
    )}
    {...props}
  >
    <div className="text-t6 leading-[140%]">{title}</div>
    <BaseSheetClose asChild>
      <Button variant="blank" size={'mini'}>
        <Icon name="cross" size={21} />
      </Button>
    </BaseSheetClose>
  </div>
);
BaseSheetHeader.displayName = 'SheetHeader';

const BaseSheetFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      ' bottom-0 w-full flex flex-row justify-between items-center p-4 border-t border-black bg-white',
      className
    )}
    {...props}
  />
);
BaseSheetFooter.displayName = 'SheetFooter';

export {
  Sheet,
  BaseSheet,
  BaseSheetPortal,
  BaseSheetOverlay,
  BaseSheetTrigger,
  BaseSheetClose,
  BaseSheetContent,
  BaseSheetHeader,
  BaseSheetFooter,
  // SheetTitle,
  // SheetDescription,
};
