/* eslint-disable @next/next/no-img-element */
import { CheckCircle, Download, FileIcon } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { CDSSMessage, MediaItem } from '../../../@types/Message';
import FileTool from './FileTool';
import MessageStatus from './MessageStatus';
import ChatTimer from '../chatTimer/ChatTimer';

interface MediaContentProps {
  message: CDSSMessage;
  isCustomer: boolean;
}
const MediaContent: React.FC<MediaContentProps> = ({ message, isCustomer }) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const media = message.medias?.[0];

  const formatFileSize = (size: string | undefined) => {
    if (!size) return '';
    return size;
  };
  useEffect(() => {
    console.log('MediaContent message', message);
  }, [message]);

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  const renderVirusStatus = () => {
    // if (!media?.needScan) return null;

    const isClean = media?.scanResult === 'No Virus detected';

    return (
      <div className="flex items-center text-sm text-gray-500">
        {isClean && (
          <>
            <div className="w-4 h-4 mr-1">
              <svg
                viewBox="0 0 24 24"
                fill="none"
                className="w-full h-full text-green-600"
              >
                <path
                  d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                  fill="currentColor"
                  fillOpacity="0.2"
                />
                <path
                  d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                  stroke="currentColor"
                  strokeWidth="2"
                />
                <path
                  d="M8 12L10.5 14.5L16 9"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <span>No virus detected</span>
          </>
        )}
      </div>
    );
  };

  const ImagePreviewModal = () => {
    if (!showPreview) return null;

    return (
      <div
        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
        onClick={() => setShowPreview(false)}
      >
        <div className="relative max-w-4xl max-h-screen bg-white rounded-lg p-2">
          <img
            src={media?.url}
            alt="Preview"
            className="max-w-full max-h-[90vh] object-contain"
          />
          <button
            className="absolute top-2 right-2 p-2 rounded-full bg-gray-800 text-white hover:bg-gray-700"
            onClick={() => setShowPreview(false)}
          >
            ✕
          </button>
        </div>
      </div>
    );
  };

  const renderMediaContent = () => {
    if (!media) return null;

    return (
      <div className="max-w-[34rem] min-w-[20rem]rounded-lg overflow-hidden bg-[#F0F0F0]">
        <div className="max-w-[34rem] min-w-[20rem] border border-black-200 rounded-md p-3 flex items-center gap-3">
          {/* File Icon Container */}
          <div className="flex-shrink-0 w-12 h-12 bg-orange-100 rounded-md flex items-center justify-center mr-[1rem]">
            <FileIcon className="w-6 h-6 text-orange-500" />
          </div>

          {/* File Information */}
          <div className="flex-grow">
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium text-gray-900">
                {media.filename}
                <Download className="inline-block ml-1 w-4 h-4 text-black-500" />
              </div>
            </div>
            <div className="flex items-center gap-2 text-sm text-black-500">
              <span>{media.contentSizeBytes}</span>
              <span className="flex items-center gap-1">
                <CheckCircle className="w-4 h-4 text-green-500" />
                {media.scanResult}
              </span>
            </div>
          </div>
        </div>

        {/* Image preview section */}
        {media.mediaType === 'image' && (
          <>
            {/* <div className="h-px p-4 w-full" /> */}
            <div className="relative border-t p-4">
              {imageLoading && (
                <div className="w-full h-48 animate-pulse bg-gray-100" />
              )}
              <img
                src={media.url}
                alt="Preview"
                className={`w-full h-auto object-cover rounded-[md]${
                  imageLoading ? 'opacity-0' : 'opacity-100'
                }`}
                onLoad={handleImageLoad}
                onError={handleImageError}
              />
            </div>
          </>
        )}
      </div>
    );
  };

  return (
    <>
      {/*<div className="flex flex-col relative">*/}
      {/*  {renderMediaContent()}*/}

      {/*  <div*/}
      {/*    className={`flex items-center mt-1 space-x-2 text-xs text-gray-500 ${*/}
      {/*      isCustomer ? 'self-end' : 'self-start'*/}
      {/*    }`}*/}
      {/*  ></div>*/}
      {/*</div>*/}
      {/*<ImagePreviewModal />*/}
      {/* image sent by agent do not show fileTool */}
      <FileTool
        message={message}
        isCustomer={isCustomer}
        isShowStt={true}
      />
      <div
        className={`flex items-center space-x-1 px-1 ${
          isCustomer ? 'self-start' : 'self-end'
        }`}
      >
        <ChatTimer
          timestamp={message.timestamp}
          className="text-xs text-gray-500"
        />
        {!isCustomer && message.status && (
          <MessageStatus status={message.status} />
        )}
        {/* {message.status && <MessageStatus status={message.status} />} */}
      </div>
    </>
  );
};

export default MediaContent;
