// MessagePage.tsx
import React, { useRef, useEffect, useState } from 'react';
import { ArrowDown } from 'lucide-react';
import MessageContent from './MessageContent';
import { CDSSMessage } from '../../../@types/Message';
import { SAASearchTerm } from '../../../@types/SAA';

interface MessagePageProps {
  currentMessages: CDSSMessage[];
  highlightedMessageId?: string;
  setSAAManualSearchTerm?: (manualSearchTerm: SAASearchTerm) => void;
}

const MessagePage: React.FC<MessagePageProps> = ({
  currentMessages,
  highlightedMessageId,
  setSAAManualSearchTerm,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const containerRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  useEffect(() => {
    console.log('currentMessages', currentMessages);
  }, [currentMessages]);
  // 自动滚动到最新消息
  useEffect(() => {
    scrollToBottom();
  }, [currentMessages]);

  // 处理消息高亮和滚动
  useEffect(() => {
    // console.log('useEffect 2');
    if (highlightedMessageId && messageRefs.current[highlightedMessageId]) {
      messageRefs.current[highlightedMessageId]?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });

      // 添加高亮动画效果
      const element = messageRefs.current[highlightedMessageId];
      element?.classList.add('highlight-animation');

      // 移除高亮效果
      setTimeout(() => {
        element?.classList.remove('highlight-animation');
      }, 2000);
    }
  }, [highlightedMessageId]);

  // 监听滚动位置
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 200;
      setShowScrollButton(!isNearBottom);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToBottom = () => {
    const timer = setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);

    return () => clearTimeout(timer);
  };

  return (
    <>
      <style>
        {`
          .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
          }

          .hide-scrollbar::-webkit-scrollbar {
            display: none;
          }

          .highlight-animation {
            animation: highlight 2s ease-in-out;
          }

          @keyframes highlight {
            0%, 100% { background-color: transparent; }
            50% { background-color: rgba(248, 160, 74, 0.2); }
          }
        `}
      </style>

      <div className="h-full relative">
        <div
          ref={containerRef}
          className="h-full overflow-y-auto p-4 space-y-4 hide-scrollbar"
        >
          {[...currentMessages]
            .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
            .map((message) => {
              const isCustomer = message.direction === 'inbound';
              return (
                <div
                  id={message.id}
                  key={message.id}
                  ref={(el) => (messageRefs.current[message.id] = el)}
                  className={`flex ${isCustomer ? 'justify-start' : 'justify-end'}`}
                >
                  <div
                    className={`flex flex-col max-w-3xl rounded-lg ${
                      isCustomer ? 'bg-white-100' : 'bg-white-100'
                    }`}
                  >
                    <span
                      className={`flex text-gray-400 ${isCustomer ? 'justify-start' : 'justify-end'}`}
                    >
                      {message.userName}
                    </span>
                    <MessageContent
                      message={message}
                      isCustomer={isCustomer}
                      setSAAManualSearchTerm={setSAAManualSearchTerm}
                    />
                  </div>
                </div>
              );
            })}
          <div ref={messagesEndRef} />
        </div>

        {showScrollButton && (
          <button
            onClick={scrollToBottom}
            className="absolute bottom-6 right-6 bg-orange-400 text-white p-2 rounded-full shadow-lg hover:bg-orange-500 transition-colors duration-200 flex items-center justify-center"
            aria-label="Scroll to latest message"
          >
            <ArrowDown className="w-5 h-5" />
          </button>
        )}
      </div>
    </>
  );
};

export default MessagePage;
