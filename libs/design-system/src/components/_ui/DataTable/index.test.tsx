import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import Button, { TButtonProps } from './index';

describe('Button', () => {
  const testId = 'test-button';
  const defaultProps: TButtonProps = {
    size: 'm',
    variant: 'primary',
    children: 'Test Button',
    testId: testId,
  };

  it('renders without crashing', () => {
    const { getByText } = render(<Button {...defaultProps} />);
    expect(getByText('Test Button')).toBeInTheDocument();
  });

  it('renders with beforeIcon and afterIcon', () => {
    const { getByTestId } = render(
      <Button
        {...defaultProps}
        beforeIcon={<div data-testid="before-icon" />}
        afterIcon={<div data-testid="after-icon" />}
      />
    );
    expect(getByTestId('before-icon')).toBeInTheDocument();
    expect(getByTestId('after-icon')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    const { getByText } = render(
      <Button
        {...defaultProps}
        onClick={handleClick}
      />
    );
    fireEvent.click(getByText('Test Button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('renders as a link when asLink is true', () => {
    const { getByTestId } = render(
      <Button
        {...defaultProps}
        asLink={true}
      />
    );
    expect(getByTestId(testId).tagName).toBe('A');
  });

  it('renders as a button when asLink is false', () => {
    const { getByTestId } = render(
      <Button
        {...defaultProps}
        asLink={false}
      />
    );
    expect(getByTestId(testId).tagName).toBe('BUTTON');
  });
});
