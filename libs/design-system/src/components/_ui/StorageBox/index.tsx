import * as Accordion from '@radix-ui/react-accordion';
import { useState } from 'react';

type TParticipant = {
  [key: string]: string;
};

interface IStorageBoxProps {
  title: string;
  data: TParticipant;
}

/**
 * StorageBox
 *
 * A component that renders an accordion with a list of key-value pairs.
 *
 * @param {string} title - The title of the accordion.
 * @param {Object} data - An object containing key-value pairs to display.
 * @returns {ReactElement} - The StorageBox component.
 */
const StorageBox: React.FC<IStorageBoxProps> = ({ title, data }) => {
  const dataArray = data ? Object?.entries(data) : [];
  const [isOpen, setIsOpen] = useState(false);

  const handleToggle = () => {
    setIsOpen((prev) => !prev);
  };

  return (
    <Accordion.Root
      key={title}
      type="single"
      collapsible
    >
      <Accordion.Item value={title}>
        <Accordion.Header>
          <Accordion.Trigger onClick={handleToggle}>
            {isOpen ? ' - ' : '+'}
            {title}
          </Accordion.Trigger>
        </Accordion.Header>
        <Accordion.Content>
          {dataArray.map(([key, value]) => {
            return (
              <p
                key={key}
                className="px-2"
              >
                <label className="font-bold">{`${key}: `}</label>
                <span className="text-[#636363]">
                  {value && value.length > 0 ? value : '--'}
                </span>
              </p>
            );
          })}
        </Accordion.Content>
      </Accordion.Item>
    </Accordion.Root>
  );
};

export default StorageBox;
