import {
    Panel,
} from '@cdss-modules/design-system';
import {
    camelCaseToWords,
} from '@cdss-modules/design-system/lib/utils';
import { FC, memo } from 'react';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
type TfieldVal = {
    name: string;
    id?: string;
    icon?: any;
    value: any;
};
type TProps = {
    loading?: boolean;
    data: any;
    onSaaContentCopy?: (contentToSet: string) => void;
    onSaaContentSend?: (contentToSet: string) => void;
    useSAAv2?: boolean;
    hasAutoMod?: boolean;
    isShowCopy?: boolean;

};
const Info: FC<TProps> = memo((props) => {
    const { data } = props
    const customerInfo = data?.customerInfo
    const fieldVal = ({ id, name, value, icon }: TfieldVal) => {
        return (
            <>
                <div
                    key={id}
                // className="grid grid-rows-2 grid-flow-col gap-2"
                >
                    <div className="row-span-1 flex items-center flex-row gap-2 col-span-11">
                        <Icon
                            name={icon}
                        // size={64}
                        />
                        <strong>{name}:</strong>
                    </div>
                    <div className="row-span-1 col-span-11 font-light text-[#636363]">
                        <p>{camelCaseToWords(value) || 'N/A'}</p>
                    </div>
                </div>
            </>
        );
    };

    const getValue = (data: any, type: string) => {

        const res = data?.reduce((arr: any[], item: any) => {
            if (item?.contactType == type) {
                arr?.push(item?.contactValue?.replace("tel:", ""));
            }
            if (type == "OTHER") {
                if (item?.contactType !== "PHONE" && item?.contactType !== "EMAIL" && item?.contactType !== "MOBILE") {
                    arr?.push(item?.contactValue);
                }
            }
            return arr;
        }, []);

        return res?.length >= 0 ? res?.join(', ') : null;
    };
    return (
        <Panel
            containerClassName="h-full overflow-hidden rounded-none"
            loading={data?.loading}
            className="justify-between"
        >
            <div className="py-2 px-4 border-grey-200">
                <div className="px-4 grid grid-cols-3 gap-6">
                    {fieldVal({
                        id: '',
                        name: 'Customer Chinese Name',
                        value: customerInfo?.chineseName || '-',
                        icon: 'userLine',
                    })}
                    {fieldVal({
                        id: '',
                        name: 'Customer English Name',
                        value: customerInfo?.englishName || '-',
                        icon: 'userLine',
                    })}
                    {fieldVal({
                        id: '',
                        name: 'Email',
                        value: getValue(customerInfo?.contacts, 'EMAIL') || 'N/A',
                        icon: 'emailLine',
                    })}
                    {fieldVal({
                        id: '',
                        name: 'Gender',
                        value: customerInfo?.gender || '-',
                        icon: 'gender',
                    })}
                    {fieldVal({
                        id: '',
                        name: 'Language',
                        value: customerInfo?.language || '-',
                        icon: 'language',
                    })}
                    {fieldVal({
                        id: '',
                        name: 'Other',
                        value: getValue(customerInfo?.contacts, 'OTHER') || '-',
                        icon: 'other',
                    })}
                    {fieldVal({
                        id: '',
                        name: 'Home Number',
                        value: getValue(customerInfo?.contacts, 'HOME') || '-',
                        icon: 'home',
                    })}
                    {fieldVal({
                        id: '',
                        name: 'Mobile Number',
                        value: getValue(customerInfo?.contacts, 'MOBILE') || getValue(customerInfo?.contacts, 'PHONE') || '-',
                        icon: 'mobile',
                    })}


                </div>
            </div>
            {/* {useSAAv2?(<SAAv2 data={{convId:data?.convId}} onSaaContentCopy={props.onSaaContentCopy} onSaaContentSend={props.onSaaContentSend} hasAutoMod={props?.hasAutoMod} isShowCopy={props?.isShowCopy}/>)
          :(<SAA data={{ SAADetail: data?.SAADetail, getSAADetail: data?.getSAADetail }} />)} */}
        </Panel>
    );
}, (prevProps: any, nextProps: any) => {
    // 自定义比较函数，只有当数据真正变化时才重渲染
    return (
        JSON.stringify(prevProps.data) ===
        JSON.stringify(nextProps.data)
    );
}
)
Info.displayName = 'Info';
export default Info;
