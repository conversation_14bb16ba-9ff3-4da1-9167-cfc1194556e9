'use client';

import { FC, memo, use, useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Panel, Tabs, TabsContent, toast, useRoute<PERSON>andler } from '@cdss-modules/design-system';
import Info from './Info';
import { History } from './History';
import IconEmptyRecords from '@cdss-modules/design-system/components/_ui/Icon/IconEmptyRecords';
import { useCustomerHistory, useCustomerInfo } from '@cdss-modules/design-system/lib/hooks/useCustomerInfo';
import { useRole } from '@cdss-modules/design-system';

type TProps = {
  customerData: any;
  headerClass?: string;
  onSaaContentCopy?: (contentToSet: string) => void;
  onSaaContentSend?: (contentToSet: string) => void;
  useSAAv2?:boolean;
  hasAutoMod?:boolean;
  isShowCopy?:boolean;
};
const CallInfo: FC<TProps> = memo((props) => {
    const { customerData } = props
    const [searchParams] = useSearchParams();
    const { globalConfig } = useRole();
    const [convId, setConversationId] = useState<string | null>('');
    const {
        customerInfo,
        loading: infoLoading,
        getCustomerInfo
    } = useCustomerInfo()
    const {
        getCustomerHistory,
        interactionsHistory,
        loading: historyLoading,
        SAADetail,
        SAADetailLoading,
        SAAMatch,
        getSAAMatch,
        getSAADetail
    } = useCustomerHistory()

    const enableCallInfo = globalConfig?.microfrontends?.['ctint-mf-message']?.enableCallInfo ?? false;

    useEffect(() => {
        if (!customerData) {
            return
        }
        if(customerData?.phoneNumber||customerData?.customerId){
            getCustomerInfo({
                customerId: customerData?.customerId,
                contactValue: customerData?.phoneNumber
            })
        }
        if(customerData?.phoneNumber){
            getCustomerHistory({
                page: 1,
                pageSize: 10,
                conversationFilter: [{
                    "type": "and",
                    "name": "phoneNum",
                    "value": customerData?.phoneNumber
                }]
            })
        }

    }, [customerData])
    useEffect(() => {
        setConversationId(searchParams?.get("conversationId"));
    }, [searchParams])
    useEffect(() => {
        getSAADetail()
    }, [])
    return (
        <Panel containerClassName="h-full rounded-none">
            {(convId || convId !== '') && convId === customerData?.conversationId ? (
                <Tabs
                    defaultTab={enableCallInfo ? 'info' : 'history'}
                    triggers={enableCallInfo ? [
                        {
                            value: 'info',
                            label: 'Info',
                        },
                        {
                            value: 'history',
                            label: 'History',
                        },
                    ] : [
                        {
                            value: 'history',
                            label: 'History',
                        },
                    ]}
                    triggerClassName="py-2 px-2 text-body"
                    className='!rounded-none'
                    headerClass = {props.headerClass}
                >
                    {enableCallInfo && (<TabsContent
                        value={'info'}
                        className="pt-6 h-0 flex-1 flex flex-col"
                    >
                        {/*saa用SAADetail和getSAADetail saav2 用SAAMatch和getSAAMatch 都是来自useCustomerHistory*/}
                        <Info

                            data={
                                { customerInfo, loading: infoLoading, SAADetail: SAADetail, getSAADetail: getSAADetail,convId:convId }

                            }
                            onSaaContentCopy={props.onSaaContentCopy}
                            onSaaContentSend={props.onSaaContentSend}
                            hasAutoMod={props?.hasAutoMod}
                            isShowCopy={props?.isShowCopy}
                            useSAAv2={props.useSAAv2}
                        />
                    </TabsContent>)}

                    <TabsContent
                        value={'history'}
                        className="pt-6 h-0 flex-1 flex flex-col"
                    >
                        <History data={interactionsHistory} loading={historyLoading} phoneNumber={customerData?.phoneNumber} />
                    </TabsContent>
                </Tabs>
            ) : (
                <div className="w-full h-full flex flex-col items-center justify-center">
                    <IconEmptyRecords size="78" />
                    <div className="text-grey-500">No selected interaction.</div>
                </div>
            )}
        </Panel>
    );
}, (prevProps: any, nextProps: any) => {
    // 自定义比较函数，只有当数据真正变化时才重渲染
    return (
        JSON.stringify(prevProps.customerData) ===
        JSON.stringify(nextProps.customerData)
    );
})
CallInfo.displayName = 'CallInfo';
export default CallInfo;
