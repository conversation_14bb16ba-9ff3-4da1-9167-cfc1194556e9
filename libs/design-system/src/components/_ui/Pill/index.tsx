import { cn } from '../../../lib/utils';
import { VariantProps, cva } from 'class-variance-authority';

export const pillVariants = cva(
  'flex items-center justify-center cursor-pointer',
  {
    variants: {
      variant: {
        primary:
          'border border-grey-200 rounded-full hover:border-primary-500 hover:bg-primary-100',
        person:
          'rounded border border-grey-200 hover:border-primary-500 hover:bg-primary-100',
        single: 'bg-status-success text-white rounded-full',
        multiple: 'bg-status-info text-white rounded-full',
        flashing: 'rounded-full animate-flashing',
        agentStatus:
          'border border-grey-200 rounded-full hover:border-black hover:bg-primary-100',
      },
      size: {
        s: 'py-1 px-2 text-sm min-w-[75px]',
        m: 'py-1 px-2 text-remark',
        //   l: 'py-3 px-4 text-lg min-w-[100px]',
        mini: 'px-2 py-[2px] text-mini',
      },
    },
    defaultVariants: {
      variant: 'primary',
    },
  }
);

export type TPillVariants = VariantProps<typeof pillVariants>;

type TCommonProps = {
  children?: React.ReactNode;
  className?: string;
  active?: boolean;
  loading?: boolean;
} & TPillVariants;

export type TPillProps = TCommonProps &
  React.ButtonHTMLAttributes<HTMLButtonElement>;

const Pill = ({
  variant = 'primary',
  size = 'm',
  children,
  className,
  active,
  loading,
  ...props
}: TPillProps) => {
  return (
    <button
      className={cn(
        'disabled:bg-grey-100 disabled:text-grey-500 disabled:border-none disabled:cursor-not-allowed',
        className,
        pillVariants({ variant, size }),
        active &&
          variant === 'primary' &&
          'bg-primary-500 border-primary-500 shadow-button-primary',
        active &&
          variant === 'person' &&
          'bg-primary-100 border-primary-900 shadow-button-common',
        active &&
          variant === 'agentStatus' &&
          'border-black bg-primary-100 shadow-button-blank',
        loading && 'bg-grey-200 animate-pulse'
      )}
      disabled={props?.disabled}
      {...(props as TPillProps)}
    >
      {children}
    </button>
  );
};

export default Pill;
