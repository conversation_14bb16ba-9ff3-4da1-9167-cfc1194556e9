interface ISeekProgressProps {
  progress: number;
}

export const SeekProgress = ({ progress }: ISeekProgressProps) => {
  return (
    <div className="flex items-center w-full">
      <input
        type="range"
        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-primary-500"
        min="0"
        max="100"
        defaultValue="0"
        value={progress}
        style={{
          background: `linear-gradient(to right, rgb(255 172 74 / var(--tw-bg-opacity)) 0%, rgb(255 172 74 / var(--tw-bg-opacity)) 0%, #e5e7eb 0%, #e5e7eb 100%)`,
        }}
        onInput={(e) => {
          const target = e.target as HTMLInputElement;
          const value =
            ((target.valueAsNumber - parseInt(target.min)) /
              (parseInt(target.max) - parseInt(target.min))) *
            100;
          target.style.background = `linear-gradient(to right, rgb(255 172 74 / var(--tw-bg-opacity)) 0%, rgb(255 172 74 / var(--tw-bg-opacity)) ${value}%, #e5e7eb ${value}%, #e5e7eb 100%)`;
        }}
        // 你可以在这里添加事件处理程序，例如 onChange
        onChange={(e) => {
          console.log('seekBar => onChange', e.target.value);
        }}
      />
    </div>
  );
};
