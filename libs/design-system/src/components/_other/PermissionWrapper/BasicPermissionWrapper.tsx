import { TglobalConfig, useRole } from '../../../context/RoleContext';
import { Loader } from '../../_ui/Loader';
import { usePermission } from '../../../context/PremissionContext';

interface IBasicPermissionWrapperProps {
  /** 子组件内容 */
  children: React.ReactNode;
  /** 权限验证函数,接收全局配置并返回布尔值 */
  permissionValidation: (
    globalConfig: TglobalConfig,

    permissions: string[]
  ) => boolean;
  customerPermissionUI?: JSX.Element;
}

/**
 * 基础权限包装器组件
 * 用于根据用户权限控制内容的显示
 *
 * @param children - 子组件内容
 * @param permissionValidation - 权限验证函数,接收全局配置并返回布尔值
 */

export const BasicPermissionWrapper: React.FC<IBasicPermissionWrapperProps> = ({
  children,
  permissionValidation,
  customerPermissionUI = null,
}) => {
  const { globalConfig, loading } = useRole();
  const { permissions } = usePermission();
  console.log('globalConfig', globalConfig);

  // 如果globalConfig還沒有回來，顯示loading
  if (loading)
    return (
      <div className="w-full h-full flex items-center justify-center py-12">
        <Loader size={64} />
      </div>
    );

  // 如果globalConfig已經回來，且沒有權限，顯示無權限的訊息
  if (globalConfig && !permissionValidation(globalConfig, permissions)) {
    if (customerPermissionUI) return customerPermissionUI;
    else
      return (
        <h2 className="p-6 font-bold text-t6">
          You are unauthorized to use this feature.
        </h2>
      );
  }

  // 如果globalConfig已經回來，且有權限，顯示children
  return <>{children}</>;
};
