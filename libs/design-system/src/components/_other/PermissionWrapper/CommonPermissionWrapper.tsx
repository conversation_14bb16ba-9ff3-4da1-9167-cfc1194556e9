import { TglobalConfig } from '../../../context/RoleContext';
import { CommonPermission } from '../../../@types/CommonPermission';
import { BasicPermissionWrapper } from './BasicPermissionWrapper';

interface CommonPermissionWrapperProps {
  customerPermissionHandler?: (
    globalConfig: TglobalConfig,
    permissions: string[]
  ) => boolean;
  children: React.ReactNode;
}

export const CommonPermissionWrapper: React.FC<
  CommonPermissionWrapperProps
> = ({ customerPermissionHandler, children }) => {
  const effectivePermissionHandler =
    customerPermissionHandler ||
    ((globalConfig, permissions) => {
      return new CommonPermission(
        globalConfig,
        permissions
      ).isPermissionEnabled('ctint-mf-interaction', '', '');
    });

  return (
    <BasicPermissionWrapper permissionValidation={effectivePermissionHandler}>
      {children}
    </BasicPermissionWrapper>
  );
};
