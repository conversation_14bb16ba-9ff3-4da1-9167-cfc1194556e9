import { CommonPermission } from '../../../@types/CommonPermission';
import { TglobalConfig } from '../../../context/RoleContext';
import { BasicPermissionWrapper } from './BasicPermissionWrapper';
import { GlobalConfigPermission } from '../../../@types/GlobalConfigPermission';
import { useTranslation } from 'react-i18next';

interface GlobalConfigPermissionWrapperProps {
  customerPermissionHandler?: (
    globalConfig: TglobalConfig,
    permissions: string[]
  ) => boolean;
  children: React.ReactNode;
}

export const GlobalConfigPermissionWrapper: React.FC<
  GlobalConfigPermissionWrapperProps
> = ({ customerPermissionHandler, children }) => {
  const { t } = useTranslation();

  const effectivePermissionHandler =
    customerPermissionHandler ||
    ((globalConfig, permissions) => {
      return new GlobalConfigPermission(globalConfig).isPermissionEnabled(
        'ctint-mf-interaction',
        'qm'
      );
    });

  return (
    <BasicPermissionWrapper
      permissionValidation={effectivePermissionHandler}
      customerPermissionUI={<p className="m-2">{t('notAvailable')}</p>}
    >
      {children}
    </BasicPermissionWrapper>
  );
};
