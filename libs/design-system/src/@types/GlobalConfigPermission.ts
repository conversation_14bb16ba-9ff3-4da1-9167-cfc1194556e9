import { mfName } from '../components/_ui/FilterComponent/api/appConfig';
import { BasicPermissionChecker } from './BasicPermission';

export class GlobalConfigPermission extends BasicPermissionChecker {
  // check if the user has permission to visit the QM page
  checkPermission: () => boolean = () => false;

  /**
   * Check if the permission is enabled for the given module, function and permission name
   * @param moduleName - The module name
   * @param functionName - The function name
   * @param permissionName - The permission name
   * @returns boolean
   */
  isPermissionEnabled: (moduleName: string, functionName: string) => boolean = (
    moduleName,
    functionName
  ) => {
    const showFunction =
      this.globalConfig?.microfrontends?.[mfName]?.['permissions']?.includes(
        `${moduleName}.${functionName}`
      ) ?? false;

    console.log(
      'this.globalConfig?.microfrontends?.[mfName]?.["permissions"]',
      this.globalConfig?.microfrontends?.[mfName]?.['permissions']
    );

    console.log('showFunction', showFunction);

    return showFunction;
  };
}
