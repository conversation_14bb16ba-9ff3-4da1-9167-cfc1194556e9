import { TglobalConfig } from '../context/RoleContext';

export abstract class BasicPermissionChecker {
  protected globalConfig?: TglobalConfig;
  protected permissions?: string[];
  protected abstract checkPermission: () => boolean;
  protected abstract isPermissionEnabled: (
    moduleName: string,
    functionName: string,
    permissionName: string
  ) => boolean;

  constructor(globalConfig?: TglobalConfig, permissions?: string[]) {
    this.globalConfig = globalConfig;
    this.permissions = permissions;
  }
}
