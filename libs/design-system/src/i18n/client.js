'use client';

import { useEffect } from 'react';
import i18next from 'i18next';
import {
  initReactI18next,
  useTranslation as useTranslationOrg,
} from 'react-i18next';
import resourcesToBackend from 'i18next-resources-to-backend';
import LanguageDetector from 'i18next-browser-languagedetector';
import { getOptions, coreLangauges, fallbackLng } from './settings';

const runsOnServerSide = typeof window === 'undefined';

const i18nextConfig = (defaultLang, languages) => {
  const basicOptions = getOptions(defaultLang, languages);
  return {
    ...basicOptions,
    detection: {
      order: ['cookie', 'path', 'htmlTag', 'navigator'],
      caches: ['cookie'],
    },
    preload: runsOnServerSide ? basicOptions?.supportedLngs : [],
    keySeparator: '.',
  };
};

export const initI18Next = async (callback, basePath = '', languageSetting) => {
  // const config = await axios
  //   .get(`${basePath}/config/ctint-global-config-dev.yaml`)
  //   .then((res) => {
  //     const data = yaml.load(res.data);
  //     return data;
  //   });
  // const languages = config.languages || {};

  const languages = languageSetting ?? {
    supportedLanguages: ['en', 'zh-HK'],
    defaultLanguage: 'en',
  };
  console.log('*** languages ***', languages, process?.env?.languages);
  const { supportedLanguages = coreLangauges, defaultLanguage = fallbackLng } =
    languages;

  const targetLanguages = supportedLanguages || coreLangauges;
  i18next
    .use(initReactI18next)
    .use(LanguageDetector)
    .use(
      resourcesToBackend(
        (language, namespace) =>
          import(`./locales/${language}/${namespace}.json`)
      )
    )
    .init(i18nextConfig(defaultLanguage, targetLanguages));
  callback && callback(targetLanguages);
};
export const addLocalesResources = (targetData, lng) => {
  if (!targetData || !lng) return;
  i18next.addResourceBundle(lng, i18nextConfig().ns, targetData);
};

export function useTranslation(lng, ns, options) {
  const ret = useTranslationOrg(ns, options);
  const { i18n } = ret;
  if (runsOnServerSide && lng && i18n.resolvedLanguage !== lng) {
    i18n.changeLanguage(lng);
  } else {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useEffect(() => {
      if (!lng || i18n.resolvedLanguage === lng) return;
      i18n.changeLanguage(lng);
    }, [lng, i18n]);

    // // eslint-disable-next-line react-hooks/rules-of-hooks
    // useEffect(() => {
    //   if (!lng || i18n.resolvedLanguage === lng) return;
    //   i18n.changeLanguage(lng);

    // }, [lng, i18n]);
  }
  return ret;
}
