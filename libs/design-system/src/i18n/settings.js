export const fallbackLng = 'en';
// For new language please refers to ISO Language Code: http://www.lingoes.net/en/translator/langcode.htm
export const coreLangauges = [fallbackLng, 'zh-HK', 'zh-CN', 'ja'];
export const defaultNS = 'translation';

export function getOptions(lng = fallbackLng, ns = defaultNS, languages) {
  return {
    // debug: true,
    supportedLngs: coreLangauges,
    fallbackLng: lng || fallbackLng,
    lng: lng || fallbackLng,
    fallbackNS: defaultNS,
    defaultNS,
    ns: ns || defaultNS,
  };
}
