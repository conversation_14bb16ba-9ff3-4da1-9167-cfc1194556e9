import SortingButton from '../../components/_ui/SortingButton';
import { ColumnDef } from '@tanstack/react-table';

export type TDummyDataType = {
  id: string;
  amount: number;
  status: string;
  email: string;
};

export const DUMMY_TABLE_DATA: TDummyDataType[] = [
  {
    id: 'm5gr84i9',
    amount: 316,
    status: 'success',
    email: '<EMAIL>',
  },
  {
    id: '3u1reuv4',
    amount: 242,
    status: 'success',
    email: '<EMAIL>',
  },
  {
    id: 'derv1ws0',
    amount: 837,
    status: 'processing',
    email: '<EMAIL>',
  },
  {
    id: '5kma53ae',
    amount: 874,
    status: 'success',
    email: '<EMAIL>',
  },
  {
    id: 'bhqecj4p',
    amount: 721,
    status: 'failed',
    email: '<EMAIL>',
  },
];

export const DUMMAY_COLUMNS: ColumnDef<TDummyDataType>[] = [
  {
    id: 'select',
    header: () => (
      <>--</>
      // <Checkbox
      //   checked={
      //     table.getIsAllPageRowsSelected() ||
      //     (table.getIsSomePageRowsSelected() && 'indeterminate')
      //   }
      //   onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
      //   aria-label="Select all"
      // />
    ),
    cell: () => (
      <>#</>
      // <Checkbox
      //   checked={row.getIsSelected()}
      //   onCheckedChange={(value) => row.toggleSelected(!!value)}
      //   aria-label="Select row"
      // />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => (
      <div className="capitalize">{row.getValue('status')}</div>
    ),
  },
  {
    accessorKey: 'email',
    header: ({ column }) => {
      return (
        <SortingButton
          sorting={column.getIsSorted()}
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Email
        </SortingButton>
      );
    },
    cell: ({ row }) => <div className="lowercase">{row.getValue('email')}</div>,
  },
  {
    accessorKey: 'amount',
    header: () => <div className="text-right">Amount</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue('amount'));

      // Format the amount as a dollar amount
      const formatted = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(amount);

      return <div className="text-right font-medium">{formatted}</div>;
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: () => {
      // const payment = row.original;

      return <>Action</>;
    },
  },
];
