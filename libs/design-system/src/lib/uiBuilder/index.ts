// Define interfaces for the structure of the components
export type TGjsComponentAttributes = {
    mlsid?: string;
    editable?: boolean;
    placeholder?: string;
    id?: string;
    fieldName?: string;
    required?: boolean;
    validation?: boolean;
}

export type TGjsComponent = {
    name: string;
    tagName?: string;
    type?: string;
    attributes?: TGjsComponentAttributes;
    components?: TGjsComponent[];
    // additional properties can be added as required based on JSON structure
}

// Function to flatten the component tree
export const flattenGjsComponents = (components: TGjsComponent[]): TGjsComponent[] => {
    const flatList: TGjsComponent[] = [];

    // Recursive function to process each component
    const processComponent = (componentList: TGjsComponent[]): void => {
        componentList.forEach((component: TGjsComponent) => {
            // Add current component to the flat list
            flatList.push({
                name: component.name,
                type: component.type,
                attributes: component.attributes
            });

            // If there are nested components, process them recursively
            if (component.components && component.components.length > 0) {
                processComponent(component.components);
            }
        });
    };

    // Start processing with the initial components array
    processComponent(components);
    return flatList;
};