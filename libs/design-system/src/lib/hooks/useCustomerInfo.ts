import { toast, useRoute<PERSON><PERSON><PERSON> } from "@cdss-modules/design-system";
import {
  fireGetContactDetail,
  fireGetCustomerHistory,
  fireGetCustomerInfo,
  fireGetInteractionHistoryDetail,
  fireGetInteractionHistoryRecording,
  fireGetInteractionHistoryRecordingId,
  fireGetSAADetail,
  GetSAAMatch
} from "@cdss-modules/design-system/lib/api";
import { extractErrorMessage } from "@cdss-modules/design-system/lib/utils";
import { useCallback, useState } from "react";
import { SAAResponse } from "@cdss-modules/design-system/@types/SAA";

export const useCustomerInfo = () => {
    const { basePath } = useRouteHandler();
    const [customerInfo, setCustomerInfo] = useState<any | null>(null);
    const [loading, setIsLoading] = useState<boolean>(false);
    const getCustomerInfo = async ({
        customerId,
        contactValue,
    }: {
        customerId?: string;
        contactValue?: string;
    }) => {
        try {
            let body: any = {};
            if (contactValue) {
                body = {
                    Contacts: [{ ContactValue: contactValue }],
                };
            } else {
                body = {
                    id: customerId,
                };
            }
            setIsLoading(true);
            const res = await fireGetCustomerInfo(body, basePath);
            setCustomerInfo(res?.data?.data || null);
        } catch (error) {
            toast({
                variant: 'error',
                title: 'Error refreshing the web',
                description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
            });
        } finally {
            setIsLoading(false);
        }
    };

    return {
        customerInfo,
        loading,
        getCustomerInfo
    }
}
type TconversationFilter = {
    type: string
    name: string
    value: string
}

export const useCustomerHistory = () => {
    const { basePath } = useRouteHandler();
    const [interactionsHistory, setInteractionsHistory] = useState<any>(null);
    const [interactionsHistoryDetail, setInteractionsHistoryDetail] = useState<any>(null);
    const [interactionsRecording, setInteractionsRecording] = useState<any>(null);
    const [loading, setIsLoading] = useState<boolean>(false);
    const [detailLoading, setDetailLoading] = useState<boolean>(false);
    const [recordingLoading, setRecordingLoading] = useState<boolean>(false);
    const [contactDetail, setContactDetail] = useState<any>(null);
    const [contactDetailLoading, setContactDetailLoading] = useState<boolean>(false);
    const [SAADetail, setSAADetail] = useState<any>(null);
  const [SAAMatch, setSAAMatch] = useState<SAAResponse | null>(null);
    const [SAADetailLoading, setSAADetailLoading] = useState<boolean>(false);
    const [SAAMatchLoading, setSAAMatchLoading] = useState<boolean>(false);
  const [currentSearchTerm, setCurrentSearchTerm] = useState<string>('');
    const getCustomerHistory = async ({
        page = 1,
        pageSize = 10,
        conversationStart,
        type = "contactHistory",
        conversationFilter
    }: {
        page: number;
        pageSize: number;
        conversationStart?: string
        type?: string
        conversationFilter: TconversationFilter[]
    }) => {
        try {
            let body: any = {
                "page": page,
                "pageSize": pageSize,
                "type": type,
                "conversationFilter": conversationFilter
            };
            if (conversationStart) {
                body["conversationStart"] = conversationStart
            }
            setIsLoading(true);
            const res = await fireGetCustomerHistory(body, basePath);

            setInteractionsHistory(res?.data?.data || null);
        } catch (error) {
            toast({
                variant: 'error',
                title: 'Error refreshing the web',
                description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
            });
        } finally {
            setIsLoading(false);
        }
    };
    const getInteractionDetail = async (convId: string) => {
        try {
            setDetailLoading(true);
            const res = await fireGetInteractionHistoryDetail(convId, basePath);
            if(res?.data?.data?.conversationType==="voice"||res?.data?.data?.conversationType==="Call"){
                getInteractionRecording(convId);
            }
            setInteractionsHistoryDetail(res?.data?.data || null);
        } catch (error) {
            toast({
                variant: 'error',
                title: 'Error refreshing the web',
                description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
            });
        } finally {
            setDetailLoading(false);
        }
    }
    const getInteractionRecording = async (convId: string) => {
        try {
            setRecordingLoading(true);
            const res = await fireGetInteractionHistoryRecordingId(convId, basePath);
            if (res?.data?.data && res?.data?.data.length > 0) {
                const rd = await Promise.all(res?.data?.data.map(async (item: any) => {
                    const r = await fireGetInteractionHistoryRecording(item?.id, basePath);
                    if (r?.data) {
                        const audioUrl = URL.createObjectURL(r?.data);
                        return audioUrl;
                    }
                    return null;
                }));
                setInteractionsRecording(rd.filter(Boolean));
            }
        } catch (error) {
            toast({
                variant: 'error',
                title: 'Error refreshing the web',
                description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
            });
        } finally {
            setRecordingLoading(false);
        }
    };

    const getContactDetail = async (convId: string) => {
        try {
            setContactDetailLoading(true);
            const res = await fireGetContactDetail(convId, basePath);
            if (res?.data?.data) {
                setContactDetail(res?.data?.data);
            }


        } catch (error) {
            toast({
                variant: 'error',
                title: 'Error refreshing the web',
                description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
            });
        } finally {
            setContactDetailLoading(false);
        }
    }
    const getSAADetail = async (keyWord?: string) => {
        try {
            let body: any ={
                search:"",
                nextPageLink:""
            };
            if(keyWord||keyWord!==""){
                body={"search":keyWord}
            }
            setSAADetailLoading(true);
            const res = await fireGetSAADetail(body, basePath);
            if (res?.data?.data) {
                setSAADetail(res?.data?.data);
            }


        } catch (error) {
            toast({
                variant: 'error',
                title: 'Error refreshing the web',
                description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
            });
        } finally {
            setSAADetailLoading(false);
        }
    }
  const getSAAMatch = async (params: { content: string, page?: number, pageSize?: number }) => {
    try {
      const { content, page = 1, pageSize = 10 } = params;

      // 检查是否是新的搜索内容
      const isNewSearch = content !== currentSearchTerm;

      // 如果是新的搜索内容，则清空已有结果
      if (isNewSearch) {
        setSAAMatch(null);
        setCurrentSearchTerm(content);
      }

      const body = {
        content,
        page,
        pageSize
      };

      setSAAMatchLoading(true);
      const res = await GetSAAMatch(body, basePath);

      if (res?.data) {
        if (isNewSearch || page === 1) {
          // 新搜索或第一页，直接设置结果
          setSAAMatch(res.data);
        } else if (SAAMatch?.data) {
          // 不是新搜索且不是第一页，合并结果
          const combinedContents = [
            ...(SAAMatch.data.contents || []),
            ...(res.data.data?.contents || [])
          ];

          // 创建合并后的数据
          const combinedData = {
            ...res.data,
            data: {
              ...res.data.data,
              contents: combinedContents
            }
          };

          setSAAMatch(combinedData);
        }
      }
    } catch (error) {
      toast({
        variant: "error",
        title: "Error refreshing the web",
        description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
      });
    } finally {
      setSAAMatchLoading(false);
    }
  };
    //直接调用 API 并返回结果，不修改状态
  // 在useCustomerHistory钩子内部
  const getSAAMatchDirectly = useCallback(async (params: { content: string, page?: number, pageSize?: number }) => {
    try {
      const { content, page = 1, pageSize = 99 } = params;

      const body = {
        content,
        page,
        pageSize
      };

      // 直接调用 API 并返回结果，不修改状态
      const res = await GetSAAMatch(body, basePath);

      if (res?.data) {
        return res.data;
      }
      return null;
    } catch (error) {
      console.error('Error getting SAA match:', extractErrorMessage(error));
      return null;
    }
  }, [basePath]); // 只依赖basePath

    return {
        interactionsHistory,
        interactionsHistoryDetail,
        getInteractionDetail,
        detailLoading,
        loading,
        getCustomerHistory,
        getInteractionRecording,
        recordingLoading,
        interactionsRecording,
        contactDetailLoading,
        contactDetail,
        getContactDetail,
        SAADetailLoading,
        SAADetail,
        getSAADetail,
        getSAAMatch,
        SAAMatch,
      getSAAMatchDirectly,
    }
}
