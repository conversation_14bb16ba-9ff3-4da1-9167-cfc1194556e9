'use client';

import { useEffect, useState } from 'react';

export const useWindowDimensions = () => {
  const [windowHeight, setWindowHeight] = useState(0);

  useEffect(() => {
    // inside useEffect, the window is always present
    const updateWindowHeight = () => {
      setWindowHeight(window.innerHeight);
    };

    updateWindowHeight(); // as soon as we are on the client, run this handler

    window.addEventListener('resize', updateWindowHeight); // works only on resize events

    return () => {
      window.removeEventListener('resize', updateWindowHeight); // clean up
    };
  }, []); // attach this once

  return windowHeight;
};
