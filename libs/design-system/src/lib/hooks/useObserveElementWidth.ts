import { useState, useRef, useEffect } from 'react';

export const useObserveElementWidth = <
  T extends HTMLElement | HTMLDivElement,
>() => {
  const [width, setWidth] = useState(0);
  const ref = useRef<T>(null);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      setWidth(entries[0].contentRect.width);
    });
    const { current } = ref;

    if (current) {
      observer.observe(current);
    }

    return () => {
      current && observer.unobserve(current);
    };
  }, []);

  return {
    width,
    ref,
  };
};
