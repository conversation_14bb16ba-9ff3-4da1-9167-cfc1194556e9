import React, {
  useState,
  useLayoutEffect,
  useRef,
  useCallback,
  useMemo,
} from 'react';
import { debounce } from 'lodash';

export const calculateContainDimensions = (
  containerWidth: number,
  containerHeight: number,
  targetNaturalWidth: number,
  targetNaturalHeight: number
): { width: number; height: number } => {
  const doRatio = targetNaturalWidth / targetNaturalHeight;
  const cRatio = containerWidth / containerHeight;
  let width: number = containerWidth;
  let height: number = containerHeight;

  if (cRatio > doRatio) {
    // Container is wider than the target's aspect ratio, so fit to height and scale width
    width = containerHeight * doRatio;
  } else {
    // Container is taller than the target's aspect ratio, so fit to width and scale height
    height = containerWidth / doRatio;
  }

  return { width, height };
};

export const getFileType = (fileName: string): 'image' | 'pdf' | 'other' => {
  if (!fileName || typeof fileName !== 'string') return 'other';
  // Extract the file extension from the URL
  const extension = (fileName?.split('.')?.pop() || '')?.toLowerCase();

  // Define lists of extensions for images and PDFs
  const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'];
  const pdfExtension = 'pdf';

  // Check the file extension and return the file type
  if (imageExtensions.includes(extension)) {
    return 'image';
  } else if (extension === pdfExtension) {
    return 'pdf';
  } else {
    return 'other';
  }
};

interface Dimensions {
  width: number;
  height: number;
}

const usePdfViewerDimensions = (
  elementWidth: number,
  elementHeight: number
): [
  React.RefObject<HTMLDivElement>,
  Dimensions,
  boolean,
  boolean,
  () => void,
] => {
  const ref = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState<Dimensions>({
    width: 0,
    height: 0,
  });
  const [resizing, setResizing] = useState<boolean>(false);
  const [documentReady, setDocumentReady] = useState<boolean>(false);
  const [layoutReady, setLayoutReady] = useState<boolean>(false);

  const updateDimensions = useCallback(() => {
    if (documentReady && ref.current) {
      setDimensions({
        width: ref.current.offsetWidth || 0,
        height: ref.current.offsetHeight || 0,
      });
      setResizing(false); // Update resizing state
      setLayoutReady(true);
    }
  }, [documentReady]);

  const updateDocumentReady = () => setDocumentReady(true);

  // Debounced version of updateDimensions
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedUpdateDimensions = useCallback(
    debounce(() => {
      updateDimensions();
    }, 300),
    [updateDimensions]
  );

  useLayoutEffect(() => {
    setResizing(true); // Assume resizing starts immediately
    debouncedUpdateDimensions(); // Initial dimensions update
    window.addEventListener('resize', debouncedUpdateDimensions);

    return () => {
      window.removeEventListener('resize', debouncedUpdateDimensions);
      debouncedUpdateDimensions.cancel(); // Cancel any pending debounced calls
    };
  }, [debouncedUpdateDimensions]);

  useLayoutEffect(() => {
    if (!documentReady) return;
    setTimeout(() => {
      debouncedUpdateDimensions();
    }, 100);
  }, [documentReady, debouncedUpdateDimensions]);

  const { width, height } = useMemo(
    () =>
      calculateContainDimensions(
        dimensions.width,
        dimensions.height,
        elementWidth,
        elementHeight
      ),
    [dimensions, elementWidth, elementHeight]
  );

  return [ref, { width, height }, resizing, layoutReady, updateDocumentReady];
};

export default usePdfViewerDimensions;
