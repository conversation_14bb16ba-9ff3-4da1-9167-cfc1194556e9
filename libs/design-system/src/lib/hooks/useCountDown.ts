import { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { toast } from '@cdss-modules/design-system';
dayjs.extend(utc);

export const useCountDown = (baseTime: number | undefined, timeout = false) => {
  const [time, setTime] = useState(baseTime); //baseTime is millisecond

  useEffect(() => {
    setTime(baseTime);
  }, [baseTime]);

  useEffect(() => {
    if (timeout) {
      const interval = setInterval(() => {
        if (time !== undefined && time > 0) {
          setTime((prevTime) => (prevTime as number) - 1000);
        }
      }, 1000);

      if (time === 0) {
        clearInterval(interval);
        setTime(0);
        toast({
          variant: 'error',
          title: 'Error',
          description: 'wrap up timout',
        });
      }

      return () => {
        clearInterval(interval);
      };
    }

    return;
  }, [time]);

  const formattedResult = dayjs(time).utc().format('mm:ss');

  return formattedResult;
};
