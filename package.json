{"name": "@ctint-mf/source", "version": "0.0.0", "license": "MIT", "scripts": {"reset-nx": "rm -rf node_modules/.cache && rm -rf .nx && nx reset", "dev": "npm run reset-nx && nx run-many --target=serve --all --maxParallel=10", "start": "nx serve", "build": "nx build", "build:all": "nx run-many --target=build --all --maxParallel=10", "env:local": "for dir in ./apps/*; do if [ -d \"$dir\" ]; then cp ./.env.local.example \"$dir\"; cp ./.env.local.example \"$dir/.env\"; fi; done", "env:uat": "for dir in ./apps/*; do if [ -d \"$dir\" ]; then cp ./.env.uat.example \"$dir\"; cp ./.env.uat.example \"$dir/.env\"; fi; done", "deploy:uat": "git tag -d release-uat-$npm_config_tag && git push origin :refs/tags/release-uat-$npm_config_tag && git tag release-uat-$npm_config_tag && git push origin release-uat-$npm_config_tag", "deploy:uat-playback": "npm run deploy:uat --tag=playback", "deploy:uat-tts": "npm run deploy:uat --tag=tts", "deploy:uat-wap": "npm run deploy:uat --tag=wap", "deploy:uat-main": "npm run deploy:uat --tag=main", "docker-build:cdss": "docker build --no-cache --build-arg APP_NAME=ctint-mf-cdss -t ctint-mf-cdss .", "docker-run:cdss": "docker run -d -p 4400:3000 --name ctint-mf-cdss-container ctint-mf-cdss", "docker-build:cpp": "docker build --no-cache --build-arg APP_NAME=ctint-mf-cpp -t ctint-mf-cpp .", "docker-run:cpp": "docker run -d -p 4500:3000 --name ctint-mf-cpp-container ctint-mf-cpp", "docker-build:interaction": "docker build --no-cache --build-arg APP_NAME=ctint-mf-interaction -t ctint-mf-interaction .", "docker-run:interaction": "docker run -d -p 4900:3000 --name ctint-mf-interaction-container ctint-mf-interaction", "docker-build:tts": "docker build --no-cache --build-arg APP_NAME=ctint-mf-tts -t ctint-mf-tts .", "docker-run:tts": "docker run -d -p 4600:3000 --name ctint-mf-tts-container ctint-mf-tts", "docker-build:template": "docker build --no-cache --build-arg APP_NAME=ctint-mf-template-t ctint-mf-template .", "docker-run:template": "docker run -d -p 4000:3000 --name ctint-mf-template-container ctint-mf-template", "docker-build:manual-queue": "docker build --no-cache --build-arg APP_NAME=ctint-mf-manual-queue -t ctint-mf-manual-queue .", "docker-run:manual-queue": "docker run -d -p 4301:3000 --name ctint-mf-manual-queue-container ctint-mf-manual-queue", "docker-build:super-dashboard": "docker build --no-cache --build-arg APP_NAME=ctint-mf-super-dashboard -t ctint-mf-super-dashboard .", "docker-run:super-dashboard": "docker run -d -p 4202:3000 --name ctint-mf-super-dashboard-container ctint-mf-super-dashboard"}, "private": true, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@apollo/client": "^3.10.1", "@grapesjs/react": "^1.0.0", "@graphql-codegen/cli": "^5.0.2", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.3.4", "@module-federation/nextjs-mf": "^8.3.11", "@module-federation/runtime": "^0.1.11", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-popover": "1.1.1", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "1.0.7", "@react-input/mask": "^1.1.1", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "^5.32.0", "@tanstack/react-table": "^8.16.0", "@tiptap/core": "^2.11.5", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/extension-subscript": "^2.11.5", "@tiptap/extension-superscript": "^2.11.5", "@tiptap/extension-table": "^2.11.5", "@tiptap/extension-table-cell": "^2.11.5", "@tiptap/extension-table-header": "^2.11.5", "@tiptap/extension-table-row": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/react-datepicker": "^6.2.0", "accept-language": "^3.0.18", "axios": "^1.6.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "core-js": "^3.37.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dompurify": "^3.0.6", "emoji-picker-react": "^4.12.0", "framer-motion": "^11.1.7", "fuse.js": "^7.0.0", "genesys-cloud-client-auth": "^0.0.2", "grapesjs": "^0.21.10", "grapesjs-blocks-basic": "^1.0.2", "html-react-parser": "^4.2.2", "http-proxy": "^1.18.1", "i18next": "^23.11.2", "i18next-browser-languagedetector": "^7.2.1", "i18next-resources-to-backend": "^1.2.1", "immer": "^10.1.1", "js-yaml": "^4.1.0", "keycloak-js": "^24.0.3", "localforage": "^1.10.0", "lodash": "^4.17.21", "lucide-react": "^0.416.0", "match-sorter": "^6.3.4", "next": "^14.0.4", "pino": "^9.0.0", "purecloud-platform-client-v2": "^188.0.1", "quill": "^2.0.3", "react": "^18.3.0", "react-arborist": "^3.4.3", "react-copy-to-clipboard": "^5.1.0", "react-datepicker": "^6.9.0", "react-datetime-picker": "^5.6.0", "react-dom": "^18.3.0", "react-dropzone": "^14.3.8", "react-highlight-within-textarea": "^3.2.1", "react-hook-form": "^7.51.3", "react-i18next": "^14.1.1", "react-icons": "^5.4.0", "react-intersection-observer": "^9.10.3", "react-pdf": "^8.0.2", "react-quill": "^2.0.0", "react-resizable-panels": "^2.0.18", "react-router-dom": "^6.23.0", "react-social-icons": "^6.18.0", "react-use-audio-player": "^2.2.0", "react-use-websocket": "^4.8.1", "reactstrap": "^9.2.2", "regenerator-runtime": "^0.14.1", "sharp": "^0.33.3", "socket.io-client": "^4.7.5", "sort-by": "^1.2.0", "swiper": "^11.1.15", "tailwind-merge": "^2.3.0", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss": "^3.4.3", "tailwindcss-animate": "^1.0.7", "tinymce": "^7.7.1", "tslib": "^2.6.2", "use-deep-compare-effect": "^1.8.1", "usehooks-ts": "^3.1.0", "uuid": "^9.0.1", "webpack": "^5.97.1", "xlsx": "^0.18.5", "yup": "^1.4.0", "zustand": "^5.0.2"}, "devDependencies": {"@nx/cypress": "18.3.4", "@nx/eslint": "18.3.4", "@nx/eslint-plugin": "18.3.4", "@nx/jest": "18.3.4", "@nx/js": "18.3.4", "@nx/next": "18.3.4", "@nx/react": "^18.3.4", "@nx/rollup": "18.3.4", "@nx/workspace": "18.3.4", "@rollup/plugin-url": "^7.0.0", "@svgr/rollup": "^8.0.1", "@swc-node/register": "~1.8.0", "@swc/cli": "~0.1.62", "@swc/core": "~1.3.85", "@swc/helpers": "~0.5.2", "@types/dompurify": "^3.0.5", "@types/jest": "^29.4.0", "@types/lodash": "^4.17.0", "@types/node": "18.16.9", "@types/react": "18.2.33", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "18.2.14", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.3.0", "@typescript-eslint/parser": "^7.3.0", "autoprefixer": "10.4.13", "babel-jest": "^29.4.1", "cypress": "^13.6.6", "eslint": "~8.57.0", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-cypress": "^2.13.4", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-unused-imports": "^3.2.0", "jest": "^29.4.1", "jest-environment-jsdom": "^29.4.1", "nx": "18.3.4", "postcss": "8.4.21", "postcss-nesting": "^12.1.2", "prettier": "^3.3.2", "react-singleton-context": "^1.0.5", "react-test-renderer": "^18.3.0", "swc-loader": "0.1.15", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "~5.4.2"}}