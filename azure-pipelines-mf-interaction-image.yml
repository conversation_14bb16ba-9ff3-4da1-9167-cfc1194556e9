trigger:
  tags:
    include:
      - 'release-image-mf-cpp*'
      - 'release-image-mf-interaction*'

variables:
  imageRepository: 'ctint-mf-interaction'
  containerRegistry: 'cdss3uatacr' 
  containerRegistryUrl: 'cdss3uatacr.azurecr.io' 
  kubernetesService: 'uat_aks_service_cdss_data_ctint' 
  tag: '1.0.$(Build.BuildId)'

stages:
- stage: Build
  displayName: Build Docker Image
  jobs:
  - job: Build
    displayName: Build and Push Docker Image
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: Docker@2
      displayName: 'Login to ACR'
      inputs:
        command: 'login'
        containerRegistry: '$(containerRegistry)'
    - task: Docker@2
      displayName: 'Build Docker Image'
      inputs:
        command: 'build'
        repository: '$(imageRepository)'
        containerRegistry: '$(containerRegistry)'
        Dockerfile: '**/Dockerfile'
        buildContext: '.'
        arguments: '--build-arg APP_NAME=$(imageRepository)'
        tags: '$(tag)'
    - task: Docker@2
      displayName: 'Push Docker Image'
      inputs:
        command: 'push'
        repository: '$(imageRepository)'
        containerRegistry: '$(containerRegistry)'
        tags: '$(tag)'