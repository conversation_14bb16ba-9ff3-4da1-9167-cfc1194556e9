{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/cypress/**/*", "!{projectRoot}/**/*.cy.[jt]s?(x)", "!{projectRoot}/cypress.config.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": []}, "plugins": [{"plugin": "@nx/next/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "devTargetName": "dev", "serveStaticTargetName": "serve-static"}}, {"plugin": "@nx/cypress/plugin", "options": {"targetName": "e2e", "openTargetName": "open-cypress", "componentTestingTargetName": "component-test", "ciTargetName": "e2e-ci"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}}, {"plugin": "@nx/rollup/plugin", "options": {"buildTargetName": "build"}}], "generators": {"@nx/react": {"application": {"babel": true}, "library": {}}, "@nx/next": {"application": {"style": "tailwind", "linter": "eslint"}}}, "targetDefaults": {"@nx/rollup:rollup": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}}