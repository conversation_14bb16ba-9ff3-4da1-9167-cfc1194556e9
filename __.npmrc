registry=https://pkgs.dev.azure.com/ctint-product-development/_packaging/cdss-ui/npm/registry/ 
                        
always-auth=true

; begin auth token
//pkgs.dev.azure.com/ctint-product-development/_packaging/cdss-ui/npm/registry/:username=ctint-product-development
//pkgs.dev.azure.com/ctint-product-development/_packaging/cdss-ui/npm/registry/:_password=N3lkdW5rdXY2ZGN1azZkbHF0ajdxbDN2d3lsbTNqemtsZm5wazMyaWhrM3VicTcyajR5cQ==
//pkgs.dev.azure.com/ctint-product-development/_packaging/cdss-ui/npm/registry/:email=npm requires email to be set but doesn't use the value
//pkgs.dev.azure.com/ctint-product-development/_packaging/cdss-ui/npm/:username=ctint-product-development
//pkgs.dev.azure.com/ctint-product-development/_packaging/cdss-ui/npm/:_password=N3lkdW5rdXY2ZGN1azZkbHF0ajdxbDN2d3lsbTNqemtsZm5wazMyaWhrM3VicTcyajR5cQ==
//pkgs.dev.azure.com/ctint-product-development/_packaging/cdss-ui/npm/:email=npm requires email to be set but doesn't use the value
; end auth token

