{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": false, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@cdss-modules/design-system": ["libs/design-system/src/index.ts"], "@cdss-modules/design-system/*": ["libs/design-system/src/*"], "@cdss-ui": ["libs/cdss-ui/src/index.ts"], "@cdss-ui/server": ["libs/cdss-ui/src/server.ts"], "@cdss/*": ["apps/ctint-mf-cdss/*"], "@cpp/*": ["apps/ctint-mf-cpp/*"], "@template/*": ["apps/ctint-mf-template/*"], "@tts/*": ["apps/ctint-mf-tts/*"], "@wap/*": ["apps/ctint-mf-wap/*"], "@info/*": ["apps/ctint-mf-info/*"], "@interaction/*": ["apps/ctint-mf-interaction/*"], "@user-admin/*": ["apps/ctint-mf-user-admin/*"]}}, "exclude": ["node_modules", "tmp"]}