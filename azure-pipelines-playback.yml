# Node.js Express Web App to Linux on Azure
# Build a Node.js Express app and deploy it to Azure as a Linux web app.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
  tags:
    include:
    - release-uat-playback

variables:

  # Azure Resource Manager connection created during pipeline creation
  azureSubscription: 'a14d89d6-dc3b-4fbf-806f-28018111ba1e'

  # Web app name
  webAppName: 'ctint-playback-portal'

  # Environment name
  environmentName: 'ctint-playback-portal'

  # Agent VM image name
  vmImageName: 'ubuntu-latest'

stages:
- stage: Build
  displayName: Build stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: $(vmImageName)

    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '20.x'
      displayName: 'Install Node.js'

    - script: |
        rm -rf node_modules && npm i
        npm run env:uat
        npx nx build playback
        cd dist/apps/playback && npm install && zip -r --symlinks '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip' .
      displayName: 'npm instal, build and zip'

    - upload: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
      artifact: drop

- stage: Deploy
  displayName: Deploy stage
  dependsOn: Build
  condition: succeeded()
  jobs:
  - deployment: Deploy
    displayName: Deploy
    environment: $(environmentName)
    pool:
      vmImage: $(vmImageName)
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Azure Web App Deploy: ctint-playback-portal'
            inputs:
              azureSubscription: $(azureSubscription)
              appType: webAppLinux
              appName: $(webAppName)
              runtimeStack: 'NODE|20-lts'
              package: $(Pipeline.Workspace)/drop/$(Build.BuildId).zip
              startUpCommand: 'node_modules/next/dist/bin/next start -- --port $PORT'