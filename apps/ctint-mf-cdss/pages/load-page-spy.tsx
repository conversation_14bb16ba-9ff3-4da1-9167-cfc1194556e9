import Script from 'next/script';
import { useEffect, useState } from 'react';
import { enhancedPageSpySession } from '../lib/enhanced-pagespy-session';
import { EnhancedPageSpyPanel } from '../components/enhanced-pagespy-panel';
import { enhancedSessionPlugin } from '../lib/enhanced-session-plugin';

export default function LoadPageSpy() {
  console.log('LoadPageSpy');
  const [showEnhancedPanel, setShowEnhancedPanel] = useState(false);
  const baseUrl = `${typeof window !== 'undefined' ? window.location.protocol : 'http:'}//ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com`;

  useEffect(() => {
    // 页面加载完成后显示增强面板
    const timer = setTimeout(() => {
      setShowEnhancedPanel(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleSessionStart = (sessionId: string) => {
    console.log('Enhanced PageSpy session started:', sessionId);
    // 这里可以添加额外的会话开始逻辑
  };

  const handleSessionStop = () => {
    console.log('Enhanced PageSpy session stopped');
    // 这里可以添加额外的会话停止逻辑
  };

  const handleSessionPause = () => {
    console.log('Enhanced PageSpy session paused');
    // 这里可以添加额外的会话暂停逻辑
  };

  const handleSessionResume = () => {
    console.log('Enhanced PageSpy session resumed');
    // 这里可以添加额外的会话恢复逻辑
  };

  return (
    <>
      <Script
        // 使用第二步：引入 SDK 文件
        src={`${baseUrl}/tracking/page-spy/index.min.js`}
        strategy="afterInteractive"
        onLoad={() => {
          // 使用第三步：实例化 PageSpy（参数都是可选的）
          // 使用第四步：在 app/page.tsx 中引入该组件
          // 之后即可使用 PageSpy，前往 https://pagespy.jikejishu.com 体验
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          window.$harbor = new DataHarborPlugin();
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          window.$rrweb = new RRWebPlugin();

          // 注册所有插件，包括我们的增强会话插件
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          [window.$harbor, window.$rrweb, enhancedSessionPlugin].forEach((p) => {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            PageSpy.registerPlugin(p);
          });

          // 实例化的参数都是可选的
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          window.$pageSpy = new PageSpy({
            api: 'ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com',
            clientOrigin: `${baseUrl}/tracking`,
            project: 'CDSS',
            logo: `${baseUrl}/ctint/mf-cdss/images/cdss-logo.svg`,
            autoRender: false, // 禁用默认渲染，使用我们的增强面板
            offline: true,
            title: localStorage.getItem('cdss-gc-username') || 'CDSS Debugger',
          });

          // 初始化增强会话管理器
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          enhancedPageSpySession.initialize(window.$pageSpy, window.$harbor, window.$rrweb);
        }}
      />

      {/* 渲染增强的PageSpy控制面板 */}
      {showEnhancedPanel && (
        <EnhancedPageSpyPanel
          onSessionStart={handleSessionStart}
          onSessionStop={handleSessionStop}
          onSessionPause={handleSessionPause}
          onSessionResume={handleSessionResume}
        />
      )}
    </>
  );
}
