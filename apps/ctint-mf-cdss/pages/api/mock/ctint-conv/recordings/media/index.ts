// pages/api/user.ts

import type { NextApiRequest, NextApiResponse } from 'next';

type DataResponse = {
  data: any;
  isSuccess: boolean;
  totalCount?: number;
  error: any;
};

const DUMMY_DATA = {
  id: 'E0DBMNLAM52MJ0GUEKVQ8I0ODO000021',
  conversationId: '-',
  mediaFile:
    'https://tribeofnoisestorage.blob.core.windows.net/music/fe5cd735cffbcce6c0b3165f6f8410b7.mp3',
};

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<DataResponse>
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS'
  );
  res.setHeader('Access-Control-Allow-Headers', '*');
  try {
    // If everything is okay, return the user data
    res.status(200).json({
      data: DUMMY_DATA,
      isSuccess: true,
      error: null,
    });
  } catch (error) {
    // If decryption fails or sourceId does not match
    res.status(403).json({
      data: null,
      isSuccess: false,
      error: 'Failed to get single recording data',
    });
  }
}
