/**
 * Enhanced PageSpy Control Panel
 * 提供可拖拽的浮窗控制界面，支持会话管理和历史记录
 */

import React, { useState, useEffect, useRef } from 'react';
import { enhancedPageSpySession, SessionState } from '../lib/enhanced-pagespy-session';

interface Position {
  x: number;
  y: number;
}

interface EnhancedPageSpyPanelProps {
  onSessionStart?: (sessionId: string) => void;
  onSessionStop?: () => void;
  onSessionPause?: () => void;
  onSessionResume?: () => void;
}

export const EnhancedPageSpyPanel: React.FC<EnhancedPageSpyPanelProps> = ({
  onSessionStart,
  onSessionStop,
  onSessionPause,
  onSessionResume
}) => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [position, setPosition] = useState<Position>({
    x: typeof window !== 'undefined' ? window.innerWidth - 320 : 300,
    y: typeof window !== 'undefined' ? window.innerHeight - 200 : 100
  });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState<Position>({ x: 0, y: 0 });
  const [currentSession, setCurrentSession] = useState<SessionState | null>(null);
  const [showHistory, setShowHistory] = useState(false);
  const [sessionHistory, setSessionHistory] = useState<any[]>([]);
  
  const panelRef = useRef<HTMLDivElement>(null);
  const dragHandleRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 监听会话状态变化
    const checkSession = () => {
      const session = enhancedPageSpySession.getCurrentSession();
      setCurrentSession(session);
    };

    const interval = setInterval(checkSession, 1000);
    checkSession(); // 初始检查

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // 加载历史会话
    loadSessionHistory();
  }, []);

  const loadSessionHistory = () => {
    if (typeof localStorage === 'undefined') return;

    const history: any[] = [];
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('pagespy-session-data-')) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}');
          if (data.metadata) {
            history.push(data);
          }
        } catch (e) {
          console.error('Failed to parse session data:', e);
        }
      }
    });

    // 按时间排序
    history.sort((a, b) => b.metadata.startTime - a.metadata.startTime);
    setSessionHistory(history);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (dragHandleRef.current?.contains(e.target as Node)) {
      setIsDragging(true);
      const rect = panelRef.current?.getBoundingClientRect();
      if (rect) {
        setDragOffset({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging && typeof window !== 'undefined') {
      const newX = Math.max(0, Math.min(window.innerWidth - 300, e.clientX - dragOffset.x));
      const newY = Math.max(0, Math.min(window.innerHeight - 100, e.clientY - dragOffset.y));
      setPosition({ x: newX, y: newY });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragOffset]);

  const handleStartRecording = () => {
    const sessionId = enhancedPageSpySession.startRecording();
    onSessionStart?.(sessionId);
  };

  const handleStopRecording = () => {
    enhancedPageSpySession.stopRecording();
    onSessionStop?.();
    loadSessionHistory(); // 重新加载历史记录
  };

  const handlePauseRecording = () => {
    enhancedPageSpySession.pauseRecording();
    onSessionPause?.();
  };

  const handleResumeRecording = () => {
    enhancedPageSpySession.resumeRecording();
    onSessionResume?.();
  };

  const handleExportSession = (sessionData?: any) => {
    let dataToExport;

    if (sessionData) {
      // 导出历史会话
      dataToExport = sessionData;
    } else if (currentSession) {
      // 导出当前会话 - 使用增强会话管理器的数据收集方法
      dataToExport = enhancedPageSpySession.getCurrentSessionData();
      if (!dataToExport) {
        console.error('Failed to get current session data');
        return;
      }
    } else {
      console.warn('No session to export');
      return;
    }

    // 创建符合rrweb-event数据类型的导出格式
    const exportData = {
      version: '1.0',
      sessionInfo: dataToExport.metadata,
      events: dataToExport.rrwebEvents,
      logs: dataToExport.logs,
      exportTime: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `pagespy-session-${dataToExport.metadata.sessionId}-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log('Session exported successfully:', dataToExport.metadata.sessionId);
  };

  const formatDuration = (startTime: number, endTime?: number) => {
    const duration = (endTime || Date.now()) - startTime;
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const panelHeight = showHistory ? '440px' : 'auto';

  return (
    <div
      ref={panelRef}
      className="enhanced-pagespy-panel"
      style={{
        position: 'fixed',
        left: `${position.x}px`,
        top: `${position.y}px`,
        width: '300px',
        height: panelHeight,
        backgroundColor: 'white',
        border: '1px solid #d9d9d9',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        zIndex: 9999,
        fontFamily: 'system-ui, -apple-system, sans-serif',
        fontSize: '14px',
        transition: 'height 0.3s ease-in-out',
        overflow: 'hidden'
      }}
      onMouseDown={handleMouseDown}
    >
      {/* 拖拽手柄 */}
      <div
        ref={dragHandleRef}
        style={{
          padding: '8px 12px',
          backgroundColor: '#f5f5f5',
          borderBottom: '1px solid #d9d9d9',
          cursor: 'move',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>PageSpy Enhanced</span>
        <button
          onClick={() => setIsMinimized(!isMinimized)}
          style={{
            border: 'none',
            background: 'none',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          {isMinimized ? '□' : '−'}
        </button>
      </div>

      {!isMinimized && (
        <div style={{ padding: '12px' }}>
          {/* 当前会话状态 */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>当前会话</div>
            {currentSession ? (
              <div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  ID: {currentSession.sessionId.split('-').pop()}
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  状态: <span style={{ 
                    color: currentSession.isRecording ? '#52c41a' : '#faad14' 
                  }}>
                    {currentSession.isRecording ? '录制中' : '已暂停'}
                  </span>
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  时长: {formatDuration(currentSession.startTime)}
                </div>
              </div>
            ) : (
              <div style={{ fontSize: '12px', color: '#999' }}>无活动会话</div>
            )}
          </div>

          {/* 控制按钮 */}
          <div style={{ display: 'flex', gap: '8px', marginBottom: '12px' }}>
            {!currentSession ? (
              <button
                onClick={handleStartRecording}
                style={{
                  flex: 1,
                  padding: '6px 12px',
                  backgroundColor: '#52c41a',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                开始记录
              </button>
            ) : (
              <>
                {currentSession.isRecording ? (
                  <button
                    onClick={handlePauseRecording}
                    style={{
                      flex: 1,
                      padding: '6px 12px',
                      backgroundColor: '#faad14',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    暂停
                  </button>
                ) : (
                  <button
                    onClick={handleResumeRecording}
                    style={{
                      flex: 1,
                      padding: '6px 12px',
                      backgroundColor: '#1890ff',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    恢复
                  </button>
                )}
                <button
                  onClick={handleStopRecording}
                  style={{
                    flex: 1,
                    padding: '6px 12px',
                    backgroundColor: '#ff4d4f',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  停止
                </button>
              </>
            )}
          </div>

          {/* 导出和历史按钮 */}
          <div style={{ display: 'flex', gap: '8px', marginBottom: '12px' }}>
            <button
              onClick={() => handleExportSession()}
              disabled={!currentSession}
              style={{
                flex: 1,
                padding: '6px 12px',
                backgroundColor: currentSession ? '#1890ff' : '#d9d9d9',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: currentSession ? 'pointer' : 'not-allowed'
              }}
            >
              导出当前
            </button>
            <button
              onClick={() => setShowHistory(!showHistory)}
              style={{
                flex: 1,
                padding: '6px 12px',
                backgroundColor: '#722ed1',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              {showHistory ? '隐藏历史' : '历史会话'}
            </button>
          </div>

          {/* 历史会话列表 */}
          {showHistory && (
            <div style={{ 
              maxHeight: '300px', 
              overflowY: 'auto',
              border: '1px solid #d9d9d9',
              borderRadius: '4px'
            }}>
              <div style={{ 
                padding: '8px', 
                backgroundColor: '#f5f5f5', 
                fontWeight: 'bold',
                borderBottom: '1px solid #d9d9d9'
              }}>
                历史会话 ({sessionHistory.length})
              </div>
              {sessionHistory.length === 0 ? (
                <div style={{ padding: '16px', textAlign: 'center', color: '#999' }}>
                  暂无历史会话
                </div>
              ) : (
                sessionHistory.map((session, index) => (
                  <div
                    key={session.metadata.sessionId}
                    style={{
                      padding: '8px',
                      borderBottom: index < sessionHistory.length - 1 ? '1px solid #f0f0f0' : 'none'
                    }}
                  >
                    <div style={{ fontSize: '12px', fontWeight: 'bold' }}>
                      {new Date(session.metadata.startTime).toLocaleString()}
                    </div>
                    <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
                      时长: {formatDuration(session.metadata.startTime, session.metadata.endTime)}
                    </div>
                    <button
                      onClick={() => handleExportSession(session)}
                      style={{
                        marginTop: '4px',
                        padding: '2px 8px',
                        fontSize: '11px',
                        backgroundColor: '#1890ff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '2px',
                        cursor: 'pointer'
                      }}
                    >
                      导出
                    </button>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
