/* eslint-disable no-useless-escape */
import React, { useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { Button } from '@cdss-modules/design-system';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import { HighlightWithinTextarea } from 'react-highlight-within-textarea';
import { DUMMY_DICTIONARY, DUMMY_META } from '../../dummy';
import { cn } from '@cdss-modules/design-system/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@cdss-modules/design-system/components/_ui/DropdownMenu';
import { BookA, Database } from 'lucide-react';
import Input from '@cdss-modules/design-system/components/_ui/Input';

const metaOptions = DUMMY_META.map((meta) => ({
  label: meta.name,
  value: `{{${meta.metaKey}}}`,
  id: meta.id,
}));

const dictionaryOptions = DUMMY_DICTIONARY.map((dict) => ({
  label: dict.name,
  value: `%_${dict.entityKey}_%`,
  id: dict.id,
}));

const extractHighlightedValues = (text: string) => {
  if (!text) return { meta: [], dict: [] };
  // Regex to match text between {{ and }}
  const metaPattern = /\{\{[^\{\}]*\}\}/g;
  // Regex to match text between %_ and _%
  const dictPattern = /%_[^%]*_%/g;

  // Extract matches for each pattern
  const matchesForPattern1 = text.match(metaPattern) || [];
  const matchesForPattern2 = text.match(dictPattern) || [];

  return {
    meta: matchesForPattern1,
    dict: matchesForPattern2,
  };
};

const relationOptions = [
  { label: 'Exists', value: 'exists' },
  { label: 'Equals to', value: 'equals' },
  { label: '≥', value: 'ge' },
  { label: '>', value: 'gt' },
  { label: '≤', value: 'le' },
  { label: '<', value: 'lt' },
];

const AutoCompleteMeta = React.memo(({ ...props }: any) => {
  const [open, setOpen] = React.useState(true);
  return (
    <DropdownMenu
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
      }}
    >
      <DropdownMenuTrigger>{props.children}</DropdownMenuTrigger>
      <DropdownMenuContent className="max-h-[200px] overflow-auto">
        {metaOptions.map((option) => (
          <DropdownMenuItem
            key={option.id}
            onClick={() => {
              // Fire an event to insert the selected metadata
              const event = new CustomEvent('insertMetaData', {
                detail: option.value?.replace('{{', ''),
              });
              document.dispatchEvent(event);
            }}
            className="text-remark"
          >
            {option.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
});
AutoCompleteMeta.displayName = 'AutoCompleteMeta';
const AutoCompleteDict = React.memo(({ ...props }: any) => {
  const [open, setOpen] = React.useState(true);
  return (
    <DropdownMenu
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
      }}
    >
      <DropdownMenuTrigger>{props.children}</DropdownMenuTrigger>
      <DropdownMenuContent className="max-h-[200px] overflow-auto">
        {dictionaryOptions.map((option) => (
          <DropdownMenuItem
            key={option.id}
            onClick={() => {
              // Fire an event to insert the selected dictionary
              const event = new CustomEvent('insertDictionary', {
                detail: option.value?.replace('%_', ''),
              });
              document.dispatchEvent(event);
            }}
            className="text-remark"
          >
            {option.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
});
AutoCompleteDict.displayName = 'AutoCompleteDict';

const SimilarityTextarea = ({ control, path, isRuleForm }: any) => {
  const { watch, setValue } = useFormContext();

  const [metadataToAdd, setMetadataToAdd] = React.useState<string>('');
  const [dictionaryToAdd, setDictionaryToadd] = React.useState<string>('');
  const [openDataPanel, setOpenDataPanel] = React.useState<boolean>(false);

  const [selection, setSelection] = React.useState<any>(null);

  const addTextBasedOnPosition = (
    text: string,
    position: number,
    value: string
  ) => {
    const firstPart = text.slice(0, position);
    const secondPart = text.slice(position);
    return `${firstPart}${value}${secondPart}`;
  };
  const currentValue = watch(`${path}.dataValue`);

  useEffect(() => {
    document.addEventListener('insertMetaData', (e: any) => {
      setValue(`${path}.dataValue`, currentValue + e?.detail);
    });
    document.addEventListener('insertDictionary', (e: any) => {
      setValue(`${path}.dataValue`, currentValue + e?.detail);
    });
    return () => {
      document.removeEventListener('insertMetaData', (e: any) => {
        setValue(`${path}.dataValue`, currentValue + e?.detail);
      });
      document.removeEventListener('insertDictionary', (e: any) => {
        setValue(`${path}.dataValue`, currentValue + e?.detail);
      });
    };
  }, [currentValue, setValue, path]);

  return (
    <div className="w-full">
      <Field
        title=""
        className={cn('w-full', !isRuleForm && 'pl-10')}
      >
        <Controller
          name={`${path}.dataValue`}
          control={control}
          rules={{ required: 'Data value is required' }}
          render={({ field }) => {
            const allInsertedValues = extractHighlightedValues(field.value);
            return (
              <>
                <div className="relative bg-white border border-black rounded-md overflow-hidden flex flex-col">
                  <div
                    className={cn(
                      'flex items-center gap-6 p-2 bg-grey-200 hidden'
                    )}
                  >
                    <div className="flex items-center gap-2">
                      <Select
                        options={dictionaryOptions}
                        placeholder="Select dictionary"
                        onChange={(v) => setDictionaryToadd(v)}
                        value={dictionaryToAdd}
                        labelClassName="h-full text-remark"
                        labelContainerClassName="h-8"
                        showSearch={true}
                      />
                      <Button
                        size="s"
                        type="button"
                        variant="secondary"
                        onClick={() => {
                          field.onChange(
                            addTextBasedOnPosition(
                              field.value,
                              selection?.anchor ?? (field?.value?.length || 0),
                              dictionaryToAdd
                            )
                          );
                        }}
                      >
                        Add Dictionary
                      </Button>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select
                        options={metaOptions}
                        placeholder="Select Metadata"
                        onChange={(v) => setMetadataToAdd(v)}
                        value={metadataToAdd}
                        labelClassName="h-full text-remark"
                        labelContainerClassName="h-8"
                        showSearch={true}
                      />
                      <Button
                        size="s"
                        type="button"
                        variant="secondary"
                        onClick={() => {
                          field.onChange(
                            addTextBasedOnPosition(
                              field.value,
                              selection?.anchor ?? (field?.value?.length || 0),
                              metadataToAdd
                            )
                          );
                        }}
                      >
                        Add Metadata
                      </Button>
                    </div>
                  </div>
                  <div className="h-[120px] relative p-2 w-full overflow-auto [&>*]:w-full *:h-full leading-loose pr-4">
                    <HighlightWithinTextarea
                      disabled={false}
                      highlight={[
                        {
                          highlight: /\{\{[^\{\}]*\}\}/g, // Regex to highlight text between {{ and }}
                          className:
                            'bg-primary-200 p-1 rounded-md text-remark mx-[1px]',
                        },
                        {
                          highlight: /%_[^%]*_%/g, // Regex to highlight text between %_ and _%
                          className:
                            'bg-tertiary-200 p-1 rounded-md text-remark mx-[1px]',
                        },
                        {
                          component: AutoCompleteMeta,
                          highlight: /\{\{/g, // Regex to match exactly {{
                        },
                        {
                          component: AutoCompleteDict,
                          highlight: /%_/g, // Regex to match exactly %_
                        },
                      ]}
                      {...field}
                      value={field?.value || ''}
                      onChange={(value, selection) => {
                        field.onChange(value);
                        setSelection(selection);
                      }}
                      placeholder=""
                    />
                    <div
                      className={cn(
                        'absolute bottom-0 right-0 p-2 !size-auto',
                        openDataPanel && 'bg-grey-100 rounded-tl-md hidden'
                      )}
                    >
                      <button
                        type="button"
                        onClick={() => setOpenDataPanel(!openDataPanel)}
                        className={cn(
                          'flex gap-x-1 text-footnote hover:opacity-60'
                        )}
                      >
                        <>
                          <div className="flex items-center gap-x-1 bg-tertiary-200 rounded-md px-1">
                            <BookA size={16} />
                            <div>{allInsertedValues?.dict?.length}</div>
                          </div>
                          <div className="flex gap-x-1  items-center  bg-primary-200 rounded-md px-1">
                            <Database size={16} />
                            <div>{allInsertedValues?.meta?.length}</div>
                          </div>
                        </>
                      </button>
                    </div>
                  </div>
                  {openDataPanel && (
                    <div
                      className={cn(
                        'relative flex flex-col gap-2 p-2 bg-grey-100 max-h-[200px] overflow-auto'
                      )}
                    >
                      <button
                        onClick={() => setOpenDataPanel(false)}
                        className="absolute top-2 right-2"
                      >
                        <Icon
                          name="cross"
                          size={16}
                        />
                      </button>
                      <div className="flex flex-col gap-2">
                        {/* <div className="font-bold text-remark">
                          Dictionary ({`${allInsertedValues?.dict?.length}`})
                        </div> */}
                        {allInsertedValues?.dict?.map((dict, index) => {
                          const label = dictionaryOptions.find(
                            (option) => option.value === dict
                          )?.label;
                          return (
                            <div
                              key={index}
                              className="flex items-center gap-2 text-footnote"
                            >
                              <div className="bg-tertiary-200 rounded-md py-[2px] px-1">
                                {dict}
                              </div>
                              <Select
                                onChange={(v) => null}
                                labelClassName="h-full text-footnote"
                                labelContainerClassName="h-6"
                                value={relationOptions[0].value}
                                options={relationOptions?.map((option) => ({
                                  id: option.value,
                                  label: option.label,
                                  value: option.value,
                                }))}
                                placeholder="Relation"
                              />
                            </div>
                          );
                        })}
                      </div>
                      <div className="flex flex-col gap-2">
                        {/* <div className="font-bold text-remark">
                          Meta Data ({`${allInsertedValues?.meta?.length}`})
                        </div> */}
                        {allInsertedValues?.meta?.map((meta, index) => {
                          const label = metaOptions.find(
                            (option) => option.value === meta
                          )?.label;
                          return (
                            <div
                              key={index}
                              className="flex items-center gap-2 text-footnote"
                            >
                              <div className="bg-primary-200 rounded-md py-[2px] px-1">
                                {meta}
                              </div>
                              <Select
                                onChange={(v) => null}
                                labelClassName="h-full text-footnote"
                                labelContainerClassName="h-6"
                                value={relationOptions[1].value}
                                options={relationOptions?.map((option) => ({
                                  id: option.value,
                                  label: option.label,
                                  value: option.value,
                                }))}
                                placeholder="Relation"
                              />
                              <div className="w-[100px]">
                                <Input
                                  value="some value"
                                  placeholder="Value"
                                  size="xs"
                                />
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              </>
            );
          }}
        />
      </Field>
    </div>
  );
};

export default SimilarityTextarea;
