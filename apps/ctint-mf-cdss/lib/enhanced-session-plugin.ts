/**
 * Enhanced Session Plugin for PageSpy
 * 提供会话管理和数据收集的增强功能
 */

import { enhancedPageSpySession } from './enhanced-pagespy-session';

export class EnhancedSessionPlugin {
  public name = 'EnhancedSessionPlugin';
  public enforce = 'post' as const;
  
  private socketStore: any = null;
  private isRecording = false;
  private sessionData: any[] = [];

  constructor() {
    // 监听会话状态变化
    this.bindSessionEvents();
  }

  public onInit({ socketStore }: { socketStore: any }) {
    this.socketStore = socketStore;
    console.log('EnhancedSessionPlugin initialized');

    // 监听public-data事件来收集数据
    socketStore.addListener('public-data', (message: any) => {
      if (this.isRecording) {
        this.collectData(message);
      }
    });

    // 添加自定义事件监听
    socketStore.addListener('enhanced-session-start', () => {
      this.startDataCollection();
    });

    socketStore.addListener('enhanced-session-stop', () => {
      this.stopDataCollection();
    });

    socketStore.addListener('enhanced-session-pause', () => {
      this.pauseDataCollection();
    });

    socketStore.addListener('enhanced-session-resume', () => {
      this.resumeDataCollection();
    });
  }

  public onMounted({ content }: { content?: HTMLDivElement }) {
    if (!content) return;

    // 添加会话状态指示器
    const statusIndicator = document.createElement('div');
    statusIndicator.id = 'enhanced-session-status';
    statusIndicator.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      padding: 4px 8px;
      background: #f0f0f0;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 12px;
      z-index: 9998;
      display: none;
    `;
    statusIndicator.textContent = '会话未激活';
    document.body.appendChild(statusIndicator);

    console.log('EnhancedSessionPlugin mounted');
  }

  public onReset() {
    this.stopDataCollection();
    this.sessionData = [];
    
    // 清理状态指示器
    const statusIndicator = document.getElementById('enhanced-session-status');
    if (statusIndicator) {
      statusIndicator.remove();
    }
    
    console.log('EnhancedSessionPlugin reset');
  }

  private bindSessionEvents() {
    // 监听增强会话管理器的状态变化
    const checkSessionStatus = () => {
      const currentSession = enhancedPageSpySession.getCurrentSession();
      const wasRecording = this.isRecording;
      this.isRecording = currentSession?.isRecording || false;

      // 状态变化时更新UI
      if (wasRecording !== this.isRecording) {
        this.updateStatusIndicator();
      }
    };

    // 定期检查会话状态
    setInterval(checkSessionStatus, 1000);
  }

  private startDataCollection() {
    this.isRecording = true;
    this.sessionData = [];
    this.updateStatusIndicator();
    console.log('Enhanced session data collection started');
  }

  private stopDataCollection() {
    this.isRecording = false;
    this.updateStatusIndicator();
    
    // 保存收集的数据
    if (this.sessionData.length > 0) {
      this.saveCollectedData();
    }
    
    console.log('Enhanced session data collection stopped');
  }

  private pauseDataCollection() {
    this.isRecording = false;
    this.updateStatusIndicator();
    console.log('Enhanced session data collection paused');
  }

  private resumeDataCollection() {
    this.isRecording = true;
    this.updateStatusIndicator();
    console.log('Enhanced session data collection resumed');
  }

  private collectData(message: any) {
    if (!this.isRecording) return;

    // 添加时间戳和会话信息
    const dataEntry = {
      timestamp: Date.now(),
      sessionId: enhancedPageSpySession.getCurrentSession()?.sessionId,
      type: message.type || 'unknown',
      data: message.data,
      source: message.role || 'client'
    };

    this.sessionData.push(dataEntry);

    // 限制内存中的数据量，避免内存溢出
    if (this.sessionData.length > 10000) {
      // 保留最新的8000条记录
      this.sessionData = this.sessionData.slice(-8000);
    }
  }

  private saveCollectedData() {
    const currentSession = enhancedPageSpySession.getCurrentSession();
    if (!currentSession) return;

    try {
      // 将收集的数据保存到localStorage
      const dataKey = `enhanced-session-data-${currentSession.sessionId}`;
      const dataToSave = {
        sessionId: currentSession.sessionId,
        startTime: currentSession.startTime,
        endTime: Date.now(),
        dataCount: this.sessionData.length,
        data: this.sessionData
      };

      localStorage.setItem(dataKey, JSON.stringify(dataToSave));
      console.log(`Saved ${this.sessionData.length} data entries for session ${currentSession.sessionId}`);
    } catch (error) {
      console.error('Failed to save collected session data:', error);
    }
  }

  private updateStatusIndicator() {
    const statusIndicator = document.getElementById('enhanced-session-status');
    if (!statusIndicator) return;

    const currentSession = enhancedPageSpySession.getCurrentSession();
    
    if (currentSession) {
      statusIndicator.style.display = 'block';
      if (this.isRecording) {
        statusIndicator.style.background = '#f6ffed';
        statusIndicator.style.borderColor = '#b7eb8f';
        statusIndicator.style.color = '#52c41a';
        statusIndicator.textContent = `录制中 (${this.sessionData.length} 条记录)`;
      } else {
        statusIndicator.style.background = '#fff7e6';
        statusIndicator.style.borderColor = '#ffd591';
        statusIndicator.style.color = '#fa8c16';
        statusIndicator.textContent = `已暂停 (${this.sessionData.length} 条记录)`;
      }
    } else {
      statusIndicator.style.display = 'none';
    }
  }

  /**
   * 获取当前收集的数据
   */
  public getCollectedData() {
    return {
      sessionData: this.sessionData,
      isRecording: this.isRecording,
      dataCount: this.sessionData.length
    };
  }

  /**
   * 清空收集的数据
   */
  public clearCollectedData() {
    this.sessionData = [];
    this.updateStatusIndicator();
  }

  /**
   * 手动触发数据保存
   */
  public saveData() {
    this.saveCollectedData();
  }
}

// 创建全局实例
export const enhancedSessionPlugin = new EnhancedSessionPlugin();
