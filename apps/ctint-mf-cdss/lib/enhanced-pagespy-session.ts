/**
 * Enhanced PageSpy Session Manager
 * 提供页面刷新/崩溃时的会话暂停和恢复功能
 */

export interface SessionState {
  sessionId: string;
  isRecording: boolean;
  startTime: number;
  lastActiveTime: number;
  pageUrl: string;
  userAgent: string;
  status: 'active' | 'paused' | 'completed';
}

export interface SessionData {
  logs: any[];
  rrwebEvents: any[];
  metadata: {
    sessionId: string;
    startTime: number;
    endTime?: number;
    pageUrl: string;
    userAgent: string;
  };
}

export class EnhancedPageSpySession {
  private static readonly STORAGE_KEY = 'pagespy-session-state';
  private static readonly DATA_KEY_PREFIX = 'pagespy-session-data-';
  private static readonly RETENTION_DAYS = 30;
  
  private currentSession: SessionState | null = null;
  private isInitialized = false;
  private pageSpy: any = null;
  private harbor: any = null;
  private rrweb: any = null;

  constructor() {
    // 只在客户端环境下绑定事件
    if (typeof window !== 'undefined') {
      this.bindPageLifecycleEvents();
    }
  }

  /**
   * 初始化会话管理器
   */
  public initialize(pageSpy: any, harbor: any, rrweb: any): void {
    this.pageSpy = pageSpy;
    this.harbor = harbor;
    this.rrweb = rrweb;
    this.isInitialized = true;

    // 检查是否有未完成的会话需要恢复
    this.checkAndRestoreSession();
  }

  /**
   * 开始新的记录会话
   */
  public startRecording(): string {
    const sessionId = this.generateSessionId();
    const now = Date.now();

    this.currentSession = {
      sessionId,
      isRecording: true,
      startTime: now,
      lastActiveTime: now,
      pageUrl: typeof window !== 'undefined' ? window.location.href : '',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
      status: 'active'
    };

    this.saveSessionState();

    // 通知插件开始数据收集
    this.notifyPlugins('enhanced-session-start');

    console.log('PageSpy 会话开始记录:', sessionId);
    return sessionId;
  }

  /**
   * 暂停当前会话
   */
  public pauseRecording(): void {
    if (this.currentSession && this.currentSession.isRecording) {
      this.currentSession.isRecording = false;
      this.currentSession.status = 'paused';
      this.currentSession.lastActiveTime = Date.now();
      this.saveSessionState();

      // 通知插件暂停数据收集
      this.notifyPlugins('enhanced-session-pause');

      console.log('PageSpy 会话已暂停:', this.currentSession.sessionId);
    }
  }

  /**
   * 恢复会话记录
   */
  public resumeRecording(): void {
    if (this.currentSession && !this.currentSession.isRecording) {
      this.currentSession.isRecording = true;
      this.currentSession.status = 'active';
      this.currentSession.lastActiveTime = Date.now();
      this.saveSessionState();

      // 通知插件恢复数据收集
      this.notifyPlugins('enhanced-session-resume');

      console.log('PageSpy 会话已恢复:', this.currentSession.sessionId);
    }
  }

  /**
   * 停止并完成当前会话
   */
  public stopRecording(): void {
    if (this.currentSession) {
      this.currentSession.isRecording = false;
      this.currentSession.status = 'completed';
      this.currentSession.lastActiveTime = Date.now();
      this.saveSessionState();

      // 通知插件停止数据收集
      this.notifyPlugins('enhanced-session-stop');

      // 保存会话数据到历史记录
      this.saveSessionToHistory();

      console.log('PageSpy 会话已完成:', this.currentSession.sessionId);
      this.currentSession = null;
      this.clearSessionState();
    }
  }

  /**
   * 获取当前会话状态
   */
  public getCurrentSession(): SessionState | null {
    return this.currentSession;
  }

  /**
   * 检查并恢复未完成的会话
   */
  private checkAndRestoreSession(): void {
    const savedState = this.loadSessionState();
    if (savedState && savedState.status !== 'completed') {
      const timeSinceLastActive = Date.now() - savedState.lastActiveTime;
      const maxInactiveTime = 5 * 60 * 1000; // 5分钟

      if (timeSinceLastActive < maxInactiveTime) {
        // 显示恢复对话框
        this.showRestoreDialog(savedState);
      } else {
        // 会话过期，清理状态
        this.clearSessionState();
      }
    }
  }

  /**
   * 显示会话恢复对话框
   */
  private showRestoreDialog(savedState: SessionState): void {
    const dialog = document.createElement('div');
    dialog.className = 'pagespy-restore-dialog';
    dialog.innerHTML = `
      <div class="pagespy-restore-overlay">
        <div class="pagespy-restore-content">
          <h3>检测到未完成的调试会话</h3>
          <p>发现一个在 ${new Date(savedState.startTime).toLocaleString()} 开始的会话</p>
          <p>是否要恢复这个会话？</p>
          <div class="pagespy-restore-buttons">
            <button id="pagespy-restore-yes" class="pagespy-btn pagespy-btn-primary">恢复会话</button>
            <button id="pagespy-restore-no" class="pagespy-btn pagespy-btn-secondary">开始新会话</button>
          </div>
        </div>
      </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      .pagespy-restore-dialog {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000;
      }
      .pagespy-restore-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .pagespy-restore-content {
        background: white;
        padding: 24px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        max-width: 400px;
        text-align: center;
      }
      .pagespy-restore-buttons {
        margin-top: 16px;
        display: flex;
        gap: 12px;
        justify-content: center;
      }
      .pagespy-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }
      .pagespy-btn-primary {
        background: #1890ff;
        color: white;
      }
      .pagespy-btn-secondary {
        background: #f5f5f5;
        color: #333;
      }
    `;

    document.head.appendChild(style);
    document.body.appendChild(dialog);

    // 绑定事件
    document.getElementById('pagespy-restore-yes')?.addEventListener('click', () => {
      this.restoreSession(savedState);
      document.body.removeChild(dialog);
      document.head.removeChild(style);
    });

    document.getElementById('pagespy-restore-no')?.addEventListener('click', () => {
      this.clearSessionState();
      document.body.removeChild(dialog);
      document.head.removeChild(style);
    });
  }

  /**
   * 恢复会话
   */
  private restoreSession(savedState: SessionState): void {
    this.currentSession = {
      ...savedState,
      lastActiveTime: Date.now(),
      status: 'active',
      isRecording: true
    };
    this.saveSessionState();
    console.log('PageSpy 会话已恢复:', this.currentSession.sessionId);
  }

  /**
   * 绑定页面生命周期事件
   */
  private bindPageLifecycleEvents(): void {
    if (typeof window === 'undefined') return;

    // 页面即将卸载时暂停会话
    window.addEventListener('beforeunload', () => {
      this.pauseRecording();
    });

    // 页面隐藏时暂停会话
    window.addEventListener('pagehide', () => {
      this.pauseRecording();
    });

    // 页面可见性变化时处理会话
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'hidden') {
          this.pauseRecording();
        } else if (document.visibilityState === 'visible' && this.currentSession) {
          this.resumeRecording();
        }
      });
    }

    // 页面获得焦点时恢复会话
    window.addEventListener('focus', () => {
      if (this.currentSession && !this.currentSession.isRecording) {
        this.resumeRecording();
      }
    });
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `pagespy-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 保存会话状态到localStorage
   */
  private saveSessionState(): void {
    if (this.currentSession && typeof localStorage !== 'undefined') {
      localStorage.setItem(EnhancedPageSpySession.STORAGE_KEY, JSON.stringify(this.currentSession));
    }
  }

  /**
   * 从localStorage加载会话状态
   */
  private loadSessionState(): SessionState | null {
    if (typeof localStorage === 'undefined') return null;
    const saved = localStorage.getItem(EnhancedPageSpySession.STORAGE_KEY);
    return saved ? JSON.parse(saved) : null;
  }

  /**
   * 清理会话状态
   */
  private clearSessionState(): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem(EnhancedPageSpySession.STORAGE_KEY);
    }
  }

  /**
   * 保存会话数据到历史记录
   */
  private saveSessionToHistory(): void {
    if (!this.currentSession) return;

    // 收集会话数据
    const sessionData: SessionData = {
      logs: this.collectHarborData(),
      rrwebEvents: this.collectRRWebData(),
      metadata: {
        sessionId: this.currentSession.sessionId,
        startTime: this.currentSession.startTime,
        endTime: Date.now(),
        pageUrl: this.currentSession.pageUrl,
        userAgent: this.currentSession.userAgent
      }
    };

    const dataKey = `${EnhancedPageSpySession.DATA_KEY_PREFIX}${this.currentSession.sessionId}`;
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(dataKey, JSON.stringify(sessionData));
    }

    // 清理过期的会话数据
    this.cleanupExpiredSessions();
  }

  /**
   * 收集Harbor插件的日志数据
   */
  private collectHarborData(): any[] {
    try {
      // 尝试从harbor插件获取数据
      if (this.harbor && typeof this.harbor.getData === 'function') {
        return this.harbor.getData();
      }

      // 如果没有直接的getData方法，尝试从localStorage获取
      if (typeof localStorage !== 'undefined') {
        const harborData = localStorage.getItem('page-spy-harbor-data');
        return harborData ? JSON.parse(harborData) : [];
      }
      return [];
    } catch (error) {
      console.warn('Failed to collect harbor data:', error);
      return [];
    }
  }

  /**
   * 收集RRWeb插件的录制数据
   */
  private collectRRWebData(): any[] {
    try {
      // 尝试从rrweb插件获取数据
      if (this.rrweb && typeof this.rrweb.getEvents === 'function') {
        return this.rrweb.getEvents();
      }

      // 如果没有直接的getEvents方法，尝试从全局变量获取
      if (typeof window !== 'undefined' && (window as any).rrwebEvents) {
        return (window as any).rrwebEvents;
      }

      return [];
    } catch (error) {
      console.warn('Failed to collect rrweb data:', error);
      return [];
    }
  }

  /**
   * 获取当前会话的实时数据（用于导出当前会话）
   */
  public getCurrentSessionData(): SessionData | null {
    if (!this.currentSession) return null;

    return {
      logs: this.collectHarborData(),
      rrwebEvents: this.collectRRWebData(),
      metadata: {
        sessionId: this.currentSession.sessionId,
        startTime: this.currentSession.startTime,
        endTime: Date.now(),
        pageUrl: this.currentSession.pageUrl,
        userAgent: this.currentSession.userAgent
      }
    };
  }

  /**
   * 清理过期的会话数据
   */
  private cleanupExpiredSessions(): void {
    if (typeof localStorage === 'undefined') return;

    const now = Date.now();
    const retentionTime = EnhancedPageSpySession.RETENTION_DAYS * 24 * 60 * 60 * 1000;

    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(EnhancedPageSpySession.DATA_KEY_PREFIX)) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}');
          if (data.metadata && (now - data.metadata.startTime) > retentionTime) {
            localStorage.removeItem(key);
          }
        } catch (e) {
          // 清理损坏的数据
          localStorage.removeItem(key);
        }
      }
    });
  }

  /**
   * 通知插件状态变化
   */
  private notifyPlugins(eventType: string): void {
    try {
      // 通过PageSpy的socketStore发送事件
      if (this.pageSpy && this.pageSpy.socketStore) {
        this.pageSpy.socketStore.dispatchEvent(eventType, {
          sessionId: this.currentSession?.sessionId,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.warn('Failed to notify plugins:', error);
    }
  }

  /**
   * 获取所有历史会话
   */
  public getHistorySessions(): SessionData[] {
    if (typeof localStorage === 'undefined') return [];

    const sessions: SessionData[] = [];

    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(EnhancedPageSpySession.DATA_KEY_PREFIX)) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}');
          if (data.metadata) {
            sessions.push(data);
          }
        } catch (e) {
          console.error('Failed to parse session data:', e);
        }
      }
    });

    // 按时间排序
    return sessions.sort((a, b) => b.metadata.startTime - a.metadata.startTime);
  }

  /**
   * 删除指定的历史会话
   */
  public deleteHistorySession(sessionId: string): boolean {
    if (typeof localStorage === 'undefined') return false;

    try {
      const dataKey = `${EnhancedPageSpySession.DATA_KEY_PREFIX}${sessionId}`;
      localStorage.removeItem(dataKey);
      return true;
    } catch (error) {
      console.error('Failed to delete session:', error);
      return false;
    }
  }

  /**
   * 清空所有历史会话
   */
  public clearAllHistorySessions(): void {
    if (typeof localStorage === 'undefined') return;

    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(EnhancedPageSpySession.DATA_KEY_PREFIX)) {
        localStorage.removeItem(key);
      }
    });
  }
}

// 创建全局实例
export const enhancedPageSpySession = new EnhancedPageSpySession();
