# Enhanced PageSpy 会话管理功能

## 功能概述

Enhanced PageSpy 为原有的 PageSpy 调试工具增加了强大的会话管理功能，解决了页面刷新、浏览器崩溃或关闭标签页时调试日志丢失的问题。

### 主要特性

1. **会话持久化** - 会话状态自动保存到 localStorage，页面刷新后不丢失
2. **智能恢复** - 检测到未完成的会话时，提供恢复选项
3. **生命周期管理** - 自动监听页面生命周期事件（beforeunload、visibilitychange等）
4. **历史会话** - 保存所有会话记录，支持查看和导出历史数据
5. **可拖拽浮窗** - 提供直观的控制界面，支持拖拽定位
6. **数据导出** - 支持导出符合 rrweb-event 格式的 JSON 文件

## 文件结构

```
apps/ctint-mf-cdss/
├── lib/
│   ├── enhanced-pagespy-session.ts      # 核心会话管理器
│   └── enhanced-session-plugin.ts       # PageSpy 自定义插件
├── components/
│   └── enhanced-pagespy-panel.tsx       # 浮窗控制面板
├── pages/
│   └── load-page-spy.tsx               # PageSpy 加载配置
└── docs/
    └── enhanced-pagespy-readme.md      # 本文档
```

## 核心组件说明

### 1. EnhancedPageSpySession (会话管理器)

负责会话的生命周期管理和数据持久化。

**主要方法：**
- `startRecording()` - 开始新的记录会话
- `pauseRecording()` - 暂停当前会话
- `resumeRecording()` - 恢复会话记录
- `stopRecording()` - 停止并完成当前会话
- `getCurrentSession()` - 获取当前会话状态
- `getHistorySessions()` - 获取所有历史会话

### 2. EnhancedSessionPlugin (自定义插件)

集成到 PageSpy 插件系统中，负责数据收集和状态同步。

**功能：**
- 监听 PageSpy 的 `public-data` 事件收集调试数据
- 提供会话状态指示器
- 自动保存会话数据到 localStorage

### 3. EnhancedPageSpyPanel (控制面板)

提供用户交互界面，支持会话控制和数据导出。

**特性：**
- 可拖拽定位
- 实时显示会话状态
- 支持查看历史会话
- 一键导出功能

## 使用方法

### 1. 基本使用

Enhanced PageSpy 会在页面加载后自动初始化。用户可以通过右下角的浮窗面板进行操作：

1. **开始记录** - 点击"开始记录"按钮开始新的调试会话
2. **暂停/恢复** - 在记录过程中可以暂停和恢复会话
3. **停止记录** - 完成调试后点击"停止"按钮结束会话
4. **导出数据** - 可以导出当前会话或历史会话的数据

### 2. 会话恢复

当页面刷新或重新打开时，如果检测到未完成的会话（5分钟内），系统会自动弹出恢复对话框：

- **恢复会话** - 继续之前的调试会话
- **开始新会话** - 放弃之前的会话，开始新的调试

### 3. 历史会话管理

点击"历史会话"按钮可以查看所有保存的会话记录：

- 显示会话开始时间和持续时长
- 支持单独导出每个历史会话
- 自动清理30天前的过期数据

## 数据格式

导出的 JSON 文件包含以下结构：

```json
{
  "version": "1.0",
  "sessionInfo": {
    "sessionId": "pagespy-1703123456789-abc123def",
    "startTime": 1703123456789,
    "endTime": 1703123556789,
    "pageUrl": "http://localhost:4400/ctint/mf-cdss",
    "userAgent": "Mozilla/5.0..."
  },
  "events": [
    // rrweb 录制事件数据
  ],
  "logs": [
    // PageSpy 收集的日志数据
  ],
  "exportTime": "2023-12-21T10:30:00.000Z"
}
```

## 配置选项

### 会话保留期

默认保留30天的会话数据，可以通过修改 `RETENTION_DAYS` 常量调整：

```typescript
private static readonly RETENTION_DAYS = 30;
```

### 浮窗初始位置

默认位置在右下角，可以通过修改初始 `position` 状态调整：

```typescript
const [position, setPosition] = useState<Position>({ 
  x: window.innerWidth - 320, 
  y: window.innerHeight - 200 
});
```

## 技术实现

### 页面生命周期监听

系统监听以下事件来管理会话状态：

- `beforeunload` - 页面即将卸载时暂停会话
- `pagehide` - 页面隐藏时暂停会话
- `visibilitychange` - 页面可见性变化时处理会话
- `focus` - 页面获得焦点时恢复会话

### 数据存储策略

- **会话状态** - 存储在 `localStorage` 的 `pagespy-session-state` 键
- **会话数据** - 存储在 `localStorage` 的 `pagespy-session-data-{sessionId}` 键
- **自动清理** - 定期清理超过保留期的数据

### 插件集成

通过 PageSpy 的插件系统集成，实现：

- 数据收集的统一管理
- 与原有功能的无缝集成
- 状态同步和事件通信

## 故障排除

### 1. 会话恢复对话框不显示

检查浏览器的 localStorage 是否被禁用或清理。

### 2. 数据导出失败

确认浏览器支持 Blob API 和文件下载功能。

### 3. 浮窗位置异常

刷新页面重置浮窗位置，或检查窗口大小变化。

## 兼容性

- 支持现代浏览器（Chrome 60+, Firefox 55+, Safari 12+）
- 需要 localStorage 支持
- 需要 ES6+ 语法支持

## 更新日志

### v1.0.0
- 初始版本发布
- 支持会话持久化和恢复
- 提供可拖拽控制面板
- 集成历史会话管理
- 支持数据导出功能
