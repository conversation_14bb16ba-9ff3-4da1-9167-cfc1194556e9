import { addLoggingToAxios } from '@cdss-modules/design-system/lib/logging';
import { apiConfig } from './config';
import axios, { AxiosHeaders } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import {
  TransferReq,
  UserSearchReq,
} from '@cdss-modules/design-system/@types/microfrontendsConfig';
import { MarkReadReq } from '@cdss-modules/design-system/@types/Message';
import { FilterParams } from '../../../ctint-mf-manual-queue/lib/api';

type UserConfigItems = {
  name: string;
  value: string;
};

type UserConfigProps = {
  filters?: UserConfigItems[];
  columns?: UserConfigItems[];
};

export const axiosInstance = axios.create({
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-message',
    previousId: 'ctint-bff-cdss',
  },
});

export const getMessages = (basePath = '', conversationId = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.getMessages.replace('{conversationId}', conversationId)}`
  );
};
export const getSupportedContent = (basePath = '', supportedContentId = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.supportedContent.replace('{supportedContentId}', supportedContentId)}`
  );
};

export const uploadMedia = (basePath = '', conversationId = '', file: File) => {
  const headers = new AxiosHeaders(axiosInstance.defaults.headers);
  headers.set('Content-Type', 'multipart/form-data');
  const formData = new FormData();
  formData.append('file', file);
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.uploadMedia.replace('{conversationId}', conversationId)}`,
    formData,
    { headers }
  );
};
export const msgTransfer = (
  basePath = '',
  conversationId = '',
  participantId = '',
  payload = {}
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.msgTransfer.replace('{conversationId}', conversationId).replace('{participantId}', participantId)}`,
    payload
  );
};
export const msgTransferCancel = (
  basePath = '',
  conversationId = '',
  consultParticipantId = ''
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.msgTransferCancel.replace('{conversationId}', conversationId).replace('{participantId}', consultParticipantId)}`
  );
};
export const wrapUp = (basePath = '', data: TransferReq) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.msgWrapUp}`,
    data
  );
};

export const getQueueInfo = async (basePath = '', type: string) => {
  const queryParams = new URLSearchParams({
    type: type,
  });
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.message.queueList}?${queryParams}`
  );
};

export const queueMember = (basePath = '', queueId = '') => {
  const params = new URLSearchParams();
  params.append('pageNum', '1');
  params.append('queueId', queueId || '');
  params.append('filterType', 'queue');
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.message.queueMember}?${params}`
  );
};

export const markRead = (
  basePath = '',
  conversionId: string,
  data: MarkReadReq
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.markRead.replace('{conversationId}', conversionId)}`,
    data
  );
};
//特殊处理 header中放入reqId 用于link up store中的message
export const sendOutboundMessage = (
  basePath = '',
  conversationId = '',
  reqId = '',
  payload = {}
) => {
  const headers = new AxiosHeaders(axiosInstance.defaults.headers);
  headers.set('reqId', reqId);
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.sendMessage.replace('{conversationId}', conversationId)}`,
    payload,
    { headers }
  );
};

export const userSearch = (basePath = '', data: UserSearchReq) => {
  console.log(data);
  if (data.keyword) {
    return axiosInstance.post(
      `${basePath}${apiConfig.paths.users.searchUsers}`,
      data
    );
  } else {
    const params = new URLSearchParams();
    params.append('pageNum', '1');
    params.append('filterType', 'all');
    return axiosInstance.get(
      `${basePath}${apiConfig.paths.users.getAllUsers}?${params}`
    );
  }
};

export const getTemplateList = (basePath = '', waba: string) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.templateMsg.getTemplateList}?waba=${waba}`
  );
};

export const getICRMCustomers = (basePath = '', payload = {}) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.ICRM.getCustomers}`,
    payload
  );
};

axiosInstance.interceptors.request.use(
  (config) => {
    // TODO: should call the auth api and set the token when program initialize
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      localStorage.setItem('deviceId', uuidv4());
      localStorage.setItem(
        'gc-access-token',
        'ofROc0Va1i74x3CKxPJ-et8g9jwo08wFkRgCzCcIYDwdAEmJMCIh-HDn9Sgw_U17WxTvtwnngeOksDWN8Sbatw'
      );
      localStorage.setItem(
        'cdss-auth-token',
        'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuZOcptbvJ01GpJkpzCbiiT6TpuU='
      );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss-authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (gcAccessToken) {
      config.headers['gc-authorization'] = 'Bearer ' + gcAccessToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }

    if (config.url?.includes('/interaction/recordings/transcript/')) {
      config.headers['requester'] = 'agent1';
    }

    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => {
    // Return response as is if the request is successful
    return response;
  },
  (error) => {
    const errorCode = (localStorage.getItem('errorCode') || '').split(',');
    if (errorCode && errorCode.includes(error?.response?.status.toString())) {
      const currUrl = window.location.href;
      if (currUrl?.indexOf('login') === -1) {
        const basePath = (window as any)?.GLOBAL_BASE_PATH;
        window.location.href = `${basePath}/login`;
      }
      localStorage.removeItem('cdss-auth-token');
      localStorage.removeItem('gc-access-token');
      localStorage.removeItem('permissions');
    }
    return Promise.reject(error);
  }
);

addLoggingToAxios(axiosInstance);

export const fireGetSortedRecordings = (queryParams: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.sort}?${queryParams}`);

export default axiosInstance;
