# Use a Node.js base image
FROM node:20-alpine as builder

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Copy all files in the root
COPY ./*.json ./
COPY ./*.js ./
COPY ./*.ts ./
COPY ./*.md ./
COPY ./*.lock ./

# Copy the whole /libs and /tools folders
COPY ./libs ./libs

# Declare build arguments
ARG APP_NAME

# Copy apps/${APP_NAME} folder
COPY ./apps/${APP_NAME} ./apps/${APP_NAME}

# Copy .env.build.example to root as .env before build
COPY ./apps/${APP_NAME}/.env.build.example ./apps/${APP_NAME}/.env

# Install dependencies
RUN npm install

# Set environment variables (adjust as necessary)
ENV NODE_ENV=production
ENV NX_DAEMON=false

# Reset the nx
RUN npx nx reset

# Build the application
RUN npx nx build ${APP_NAME} --prod

# Start a new stage from node alpine to keep the image small
FROM node:20-alpine as runner

# Install yq rsync
RUN apk add --no-cache yq rsync

WORKDIR /app

# Declare build arguments, expect PORT to be passed in or use 3000 as default
ARG APP_NAME
ENV APP_NAME_ENV=${APP_NAME}

# Copy the script to replace the basepath
COPY ./apps/${APP_NAME}/replace-basepath-standalone.sh /app

# make backup of dist folder
RUN mkdir orign_dist

# Copy .env.uat.example to root as .env, replace if already exists
COPY --from=builder /app/apps/${APP_NAME}/.env.uat.example /app/.env

# Copy the standalone from the builder stage
COPY --from=builder /app/dist/apps/${APP_NAME}/.next/standalone /app/orign_dist

# Copy the public from the builder stage
COPY --from=builder /app/dist/apps/${APP_NAME}/public /app/orign_dist/apps/${APP_NAME}/public

# Copy the static form the builder stage
COPY --from=builder /app/dist/apps/${APP_NAME}/.next/static /app/orign_dist/dist/apps/${APP_NAME}/.next/static

# Ensure your script is executable
RUN chmod +x /app/replace-basepath-standalone.sh

# RUN /app/replace-basepath-standalone.sh

# Expose the port the app runs on
EXPOSE 3000

CMD mkdir -p /app/orign_dist/apps/$APP_NAME_ENV/public/config  && cp /app/orign_dist/apps/$APP_NAME_ENV/public/global-config/ctint-global-config-prod.yaml /app/orign_dist/apps/$APP_NAME_ENV/public/config/ctint-global-config-prod.yaml && /app/replace-basepath-standalone.sh && HOSTNAME="0.0.0.0" node apps/$APP_NAME_ENV/server.js