# Node.js Express Web App to Linux on Azure
# Build a Node.js Express app and deploy it to Azure as a Linux web app.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
  tags:
    include:
    - release-uat-wap

variables:

  # Azure Resource Manager connection created during pipeline creation
  azureSubscription: '6d430df9-da9b-44f8-bfad-7ba8fcd14c52'

  # Web app name
  webAppName: 'ctint-msg'

  # Environment name
  environmentName: 'ctint-msg'

  # Agent VM image name
  vmImageName: 'ubuntu-latest'

stages:
- stage: Build
  displayName: Build stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: $(vmImageName)

    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '20.x'
      displayName: 'Install Node.js'

    - script: |
        rm -rf node_modules && npm i
        npm run env:uat
        npx nx build ctint-mf-wap
        cd dist/apps/ctint-mf-wap && npm install && zip -r --symlinks '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip' .
      displayName: 'npm instal, build and zip'

    - upload: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
      artifact: drop

- stage: Deploy
  displayName: Deploy stage
  dependsOn: Build
  condition: succeeded()
  jobs:
  - deployment: Deploy
    displayName: Deploy
    environment: $(environmentName)
    pool:
      vmImage: $(vmImageName)
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureWebApp@1
            displayName: 'Azure Web App Deploy: ctint-mf-wap'
            inputs:
              azureSubscription: $(azureSubscription)
              appType: webAppLinux
              appName: $(webAppName)
              runtimeStack: 'NODE|20-lts'
              package: $(Pipeline.Workspace)/drop/$(Build.BuildId).zip
              startUpCommand: 'node_modules/next/dist/bin/next start'