trigger: none

variables:
  containerRegistry: 'cdss3uatacr' 
  imageRepository: 'ctint/ctint-mf-interaction'
  appName: 'ctint-mf-interaction'
  regionName: 'ap-east-1'
  awsCredentials: 'prd_aws_cdss3_ecr'

stages:
- stage: Build
  displayName: Build Docker Image
  jobs:
  - job: Build
    displayName: Build and Push Docker Image
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: Docker@2
      displayName: 'Build Docker Image'
      inputs:
        command: 'build'
        repository: '$(imageRepository)'
        Dockerfile: '**/Dockerfile-aws'
        buildContext: '.'
        arguments: '--build-arg APP_NAME=$(appName)'
    - task: ECRPushImage@1
      inputs:
        awsCredentials: '$(awsCredentials)'
        regionName: '$(regionName)'
        imageSource: 'imagename'
        sourceImageName: '$(imageRepository)'
        sourceImageTag: $(Build.BuildId)
        pushTag: latest
        repositoryName: $(imageRepository)
       
